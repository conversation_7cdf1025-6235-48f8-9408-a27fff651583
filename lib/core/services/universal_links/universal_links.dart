import 'dart:async';

import 'package:app_links/app_links.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/game/presenter/storage/game_storage.dart';
import 'package:quycky/app/features/home/<USER>/store/promotion_store.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/use_cases/open_to_friend_match_usecase.dart';
import 'package:quycky/core/utils/use_cases/open_to_invited_game_room_usecase.dart';

class UniversalLinks {
  final GameStorage _gameStorage;
  late final AppLinks _appLinks;
  static late StreamSubscription _sub;
  final OpenToInvitedGameRoomUseCase _openToInvitedGameRoomUseCaseUseCase;
  final OpenToFriendMatchUsecase _openToFriendMatchUsecase;

  UniversalLinks(this._gameStorage, this._openToInvitedGameRoomUseCaseUseCase,
      this._openToFriendMatchUsecase) {
    init();
  }
  void dispose() {
    _sub.cancel();
  }

  void closePromotion(String promoId) {
    final PromotionStore promotionStore = Modular.get<PromotionStore>();
    final resPromo = promotionStore.getPromotionById(promoId);
    if (resPromo != null) {
      promotionStore.changePromotionData(resPromo.copyWith(status: 'claimed'));
    }
  }

  void doWorks(String link) async {
    if (link.contains('/invite')) {
      final l = link.split('/');
      print('roomName=${l.last}=>');
      //${t.getPromotionStoreData()}');
      await _gameStorage.setGameRoomWaiting(roomName: l.last);
      if (Modular.to.path == AppRoutes.initialRoute) {
        return;
      }
      _openToInvitedGameRoomUseCaseUseCase(OpenToInvitedGameRoomData(
          roomName: l.last, fromForegroundNotification: false));
      return;
    }
    if (link.contains('/ticket')) {
      final l = link.split('/');
      print('ticket: ${l.last}=>');
      _openToFriendMatchUsecase(l.last);
      return;
    }

    if (link.contains('/redeem/prize/')) {
      final l = link.split('/');
      final promoId =
          l.last.split('?').first.replaceAll(RegExp(r'[^a-zA-Z0-9]'), '');
      closePromotion(promoId);
    }
  }

  void verifyInitialLink() async {
    final String? link = await _appLinks.getInitialLinkString();

    if (link != null) {
      doWorks(link);
    }
  }

  Future<void> init() async {
    _appLinks = AppLinks();
    verifyInitialLink();
    _sub = _appLinks.uriLinkStream.listen((uri) {
      doWorks(uri.toString());
    }, onError: (err) {
      print('Err=>$err');
    });
  }
}
//   void verifyInitialLink() async {
//     final link = await getInitialLink();
//     print('Log=Init=>Res$link');
//     if (link != null) {
//       doWorks(link);
//     }
//   }

//   Future<void> init() async {
//     verifyInitialLink();
//     _sub = linkStream.listen((String? link) {
//       doWorks(link ?? '');
//     }, onError: (err) {
//       print('Err=>$err');
//     });
//   }
// }
