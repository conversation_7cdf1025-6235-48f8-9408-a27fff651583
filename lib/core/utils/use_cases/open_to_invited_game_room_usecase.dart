import 'dart:core';
import 'dart:io';

import 'package:dartz/dartz.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/game/presenter/storage/game_storage.dart';
import 'package:quycky/app/features/user/presenter/storage/user_storage.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/show_message.dart';

class OpenToInvitedGameRoomData {
  final String roomName;
  final bool fromForegroundNotification;
  OpenToInvitedGameRoomData(
      {required this.roomName, this.fromForegroundNotification = true});
}

class OpenToInvitedGameRoomUseCase
    implements UseCase<bool, OpenToInvitedGameRoomData> {
  final UserStorage _userStorage;
  final GameStorage _gameStorage;

  OpenToInvitedGameRoomUseCase(this._userStorage, this._gameStorage) : super();

  @override
  Future<Either<Failure, bool>> call(OpenToInvitedGameRoomData data) async {
    try {
      if ((await _userStorage.getUserToken()).isNotEmpty) {
        await _gameStorage.setGameRoomWaiting(roomName: data.roomName);
        try {
          // bool goToGameLobby = true;
          // if (Modular.to.path == AppRoutes.initialRoute) {
          //   final userStore = Modular.get<UserStore>();
          //   final userController = Modular.get<UserController>();
          //   await userStore.getLoginResultData();

          //   goToGameLobby = await userController.doInitialVerification(goToHome: true);
          // }
          // if (goToGameLobby) {
          setTimeout(
              callback: () => ShowMessage(
                  message: 'Opening game room...',
                  callback: () => Modular.to.pushNamed(AppRoutes.gameLobby(),
                      arguments: {'roomName': data.roomName}),
                  duration: Duration(seconds: 3),
                  noButton: true),
              duration:
                  Duration(seconds: data.fromForegroundNotification ? 0 : 3));
          // setTimeout(
          //     callback: () => Modular.to.pushNamed(AppRoutes.gameLobby()),
          //     duration:
          //         Duration(seconds: data.fromForegroundNotification ? 0 : 4));
          // }
          const Right(true);
        } catch (e) {
          debugPrint('Err:=>$e');
        }
      }
    } catch (e) {
      Left(e);
    }
    return const Right(false);
  }
}
