import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/app/features/friendship/domain/usecases/add_player_as_friend_immediately_usecase.dart';
import 'package:quycky/app/features/game/data/datasources/game_datasource_implementation.dart';
import 'package:quycky/app/features/game/data/repositories/game_repository_implementation.dart';
import 'package:quycky/app/features/game/domain/usecases/get_game_invitations_by_user_id_usecase.dart';
import 'package:quycky/app/features/game/domain/usecases/invite_friend_to_play_usecase.dart';
import 'package:quycky/app/features/game/presenter/controllers/game_controller.dart';
import 'package:quycky/app/features/game/presenter/pages/game_end_page.dart';
import 'package:quycky/app/features/game/presenter/pages/game_page.dart';
import 'package:quycky/app/features/game/presenter/pages/lobby_page.dart';
import 'package:quycky/app/features/game/presenter/pages/read_game_qrcode_page.dart';
import 'package:quycky/app/features/game/presenter/store/game_countdown_store.dart';
import 'package:quycky/app/features/game/presenter/store/game_friend_invite_store.dart';
import 'package:quycky/app/features/game/presenter/store/game_progress_store.dart';
import 'package:quycky/app/features/game/presenter/store/game_round_info_store.dart';
import 'package:quycky/app/features/game/presenter/store/game_store.dart';
import 'package:quycky/app/modules/guards/auth_guard.dart';
import 'package:quycky/core/utils/app_routes.dart';

class GameModule extends Module {
  @override
  final List<Bind> binds = [
    Bind.lazySingleton((i) => GameStore()),
    Bind.lazySingleton((i) => GameFriendInviteStore()),
    Bind.lazySingleton((i) => GameRoundInfoStore()),
    Bind.lazySingleton((i) => GameProgressStore()),
    Bind.lazySingleton((i) => GameCountdownStore()),
    Bind.factory((i) => InviteFriendToPlayUseCase(i())),
    Bind.factory((i) => AddPlayerAsFriendImmediatelyUseCase(i())),
    Bind.singleton((i) => GameController(i(), i(), i(), i(), i(), i(), i(), i(),
        i(), i(), i(), i(), i(), i(), i(), i())),
  ];

  @override
  final List<ModularRoute> routes = [
    ChildRoute(AppRoutes.gameLobby(complete: false), child: (_, args) {
      if (args.data != null) {
        final data = args.data as Map<String, dynamic>;
        final friend = data.containsKey('friend') ? args.data['friend'] : null;
        final roomName =
            data.containsKey('roomName') ? args.data['roomName'] : '';
        final keepPlaying =
            data.containsKey('keepPlaying') ? args.data['keepPlaying'] : false;
        final isGameStarted = data.containsKey('isGameStarted')
            ? args.data['isGameStarted']
            : false;
        return LobbyPage(
          friend: friend,
          roomName: roomName,
          keepPlaying: keepPlaying,
          isGameStarted: isGameStarted,
        );
      }
      return LobbyPage();
    }, guards: [AuthGuard()], transition: TransitionType.fadeIn),
    ChildRoute(AppRoutes.gamePlay(complete: false),
        child: (_, args) => const GamePage(),
        transition: TransitionType.fadeIn),
    ChildRoute(AppRoutes.gameEnd(complete: false),
        child: (_, args) => const GameEndPage(),
        transition: TransitionType.fadeIn),
    ChildRoute(AppRoutes.readGameQrCode(complete: false),
        child: (_, args) => ReadGameQrcodePage(),
        transition: TransitionType.upToDown),
  ];
}
