import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_entity.dart';
import 'package:quycky/app/features/friendship/domain/request_entities/invite_request.dart';
import 'package:quycky/app/features/friendship/domain/usecases/add_player_as_friend_immediately_usecase.dart';
import 'package:quycky/app/features/friendship/domain/usecases/get_friendships_usecase.dart';
import 'package:quycky/app/features/friendship/presenter/store/friendship_store.dart';
import 'package:quycky/app/features/game/domain/dto/game_message_dto.dart';
import 'package:quycky/app/features/game/domain/dto/player_entering_game_dto.dart';
import 'package:quycky/app/features/game/domain/dto/player_send_answer_dto.dart';
import 'package:quycky/app/features/game/domain/dto/player_send_pontuation_dto.dart';
import 'package:quycky/app/features/game/domain/dto/start_game_dto.dart';
import 'package:quycky/app/features/game/domain/entities/answer_entity.dart';
import 'package:quycky/app/features/game/domain/entities/friend_invite_params_entity.dart';
import 'package:quycky/app/features/game/domain/entities/game_entity.dart';
import 'package:quycky/app/features/game/domain/entities/game_friend_invite_entity.dart';
import 'package:quycky/app/features/game/domain/entities/game_round_info_entity.dart';
import 'package:quycky/app/features/game/domain/entities/pontuation_entity.dart';
import 'package:quycky/app/features/game/domain/entities/question_option_entity.dart';
import 'package:quycky/app/features/game/domain/entities/room_entity.dart';
import 'package:quycky/app/features/game/domain/entities/socket_user_entity.dart';
import 'package:quycky/app/features/game/domain/entities/user_image_memory_data.dart';
import 'package:quycky/app/presenter/app_widget.dart';
import 'package:quycky/app/widgets/confirmation_dialog.dart';
import 'package:quycky/core/enumerators/egame_action.dart';
import 'package:quycky/app/features/game/domain/usecases/invite_friend_to_play_usecase.dart';
import 'package:quycky/app/features/game/presenter/consts/possible_shared_invite_messages.dart';
import 'package:quycky/app/features/game/presenter/storage/game_storage.dart';
import 'package:quycky/app/features/game/presenter/store/game_countdown_store.dart';
import 'package:quycky/app/features/game/presenter/store/game_friend_invite_store.dart';
import 'package:quycky/app/features/game/presenter/store/game_progress_store.dart';
import 'package:quycky/app/features/game/presenter/store/game_round_info_store.dart';
import 'package:quycky/app/features/game/presenter/store/game_store.dart';
import 'package:quycky/app/features/home/<USER>/store/promotion_store.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/features/user/domain/usecases/get_number_of_rounds_of_logged_player_usecase.dart';
import 'package:quycky/app/features/user/presenter/controllers/user_controller.dart';
import 'package:quycky/app/features/user/presenter/storage/user_storage.dart';
import 'package:quycky/app/features/user/presenter/store/user_store.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:quycky/app/services/game_socket/game_socket_service.dart';
import 'package:quycky/core/entities/abs_mappable.dart';
import 'package:quycky/core/usecase/errors/failure.dart';
import 'package:quycky/core/usecase/usecase.dart';
import 'package:quycky/core/utils/app_env.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/show_generic_dialog.dart';
import 'package:quycky/core/utils/show_message.dart';
import 'package:quycky/core/utils/use_cases/get_image_from_url_uint8list_usecase.dart';
import 'package:share_plus/share_plus.dart';
import 'package:socket_io_client/socket_io_client.dart' as IO;
import 'package:web_socket_channel/io.dart';
import 'package:web_socket_channel/web_socket_channel.dart';

class GameController {
  final userResponseTextEditController = TextEditingController();
  final GameStorage _gameStorage;
  final GameSocketService _gameSocketService;
  bool _getPlayersImagesAlreadyCalled = false;
  bool _isModalBottomSheetOpened = false;
  // final _userIdentifierTemp = '5b8c0f1f-bda2-4fb8-8941-69108ad0a2ae';
  final int _totalTimeAnswering = 8;
  // final int _totalTimeReviewingAnwers = 8;
  IO.Socket? socket;
  final UserStore _userStore;
  final FriendshipStore _friendshipStore;
  bool _requestNewBot = true;
  final GameStore _gameStore;
  final GameFriendInviteStore _gameFriendInviteStore;
  final GetFriendshipsUseCase _getFriendshipsUseCase;
  final AddPlayerAsFriendImmediatelyUseCase
      _AddPlayerAsFriendImmediatelyUseCase;
  final GameRoundInfoStore _gameRoundInfoStore;
  final GameCountdownStore _gameCountdownStore;
  final GameProgressStore _gameProgressStore;
  final InviteFriendToPlayUseCase _inviteFriendToPlayUseCase;
  final PromotionStore _promotionStore;
  final UserStorage _userStorage;
  final GetImageUint8ListFromUrlUseCase _getImageUint8ListFromUrlUseCase;
  final String _userToken = '';
  DateTime lastEnterGamer = DateTime.now().subtract(Duration(seconds: 30));
  String get connectionId => _gameSocketService.connectionId;
  bool _isGameCancelRequested = false;
  Timer? _startSocketTimer;
  bool alreadyGavePoint = false;
  int times = 0;
  FriendInviteParamsEntity _friendInviteParamsEntity =
      FriendInviteParamsEntity(id: '', friendIdentifier: '', roomName: '');
  final GetNumberOfRoundsOfLoggedPlayerUseCase
      _getNumberOfRoundsOfLoggedPlayerUseCase;
  UserEntity? _friendInvitedData;
  WebSocketChannel? channel;
  UserEntity? _predeterminedFriend;
  final bool _isGameStarted = false;
  int _timesWritingRoomName = 0;
  Timer? _emptyLobbyTimer;
  final String socketConnectionErrorMessage =
      "There was a problem with the connection, please check your connection and try again later.";

  GameController(
      this._inviteFriendToPlayUseCase,
      this._AddPlayerAsFriendImmediatelyUseCase,
      this._getImageUint8ListFromUrlUseCase,
      this._getFriendshipsUseCase,
      this._gameCountdownStore,
      this._userStore,
      this._gameStore,
      this._gameRoundInfoStore,
      this._gameProgressStore,
      this._friendshipStore,
      this._promotionStore,
      this._gameFriendInviteStore,
      this._gameStorage,
      this._userStorage,
      this._getNumberOfRoundsOfLoggedPlayerUseCase,
      this._gameSocketService) {
    _isGameCancelRequested = false;
    getNumberOfGamesPlayedByLoggedUser();
    _setupSocketHandlers();
  }

  void _setupSocketHandlers() {
    // Configura handlers para diferentes tipos de mensagem
    _gameSocketService.addMessageHandler(EGameAction.welcome, (message) {
      onWelcome(message.data);
    });

    _gameSocketService.addMessageHandler(EGameAction.userEntered, (message) {
      setRoomUsers(message.data);
    });

    _gameSocketService.addMessageHandler(EGameAction.gameStarting, (message) {
      onRoomIsCompleted(message.data);
    });

    _gameSocketService.addMessageHandler(EGameAction.gameCancelled, (message) {
      onGameCancelled(message.data);
    });

    _gameSocketService.addMessageHandler(EGameAction.startRound, (message) {
      onStartRound(message.data);
    });

    _gameSocketService.addMessageHandler(EGameAction.updateAnswers, (message) {
      onUpdateAnswers(message.data);
    });

    _gameSocketService.addMessageHandler(EGameAction.updatePontuations,
        (message) {
      onUpdatePontuations(message.data);
    });

    _gameSocketService.addMessageHandler(EGameAction.reviewRound, (message) {
      onReviewRound(message.data);
    });

    _gameSocketService.addMessageHandler(EGameAction.playerReceivePontuation,
        (message) {
      onPlayerReceivePontuation(message.data);
    });

    _gameSocketService.addMessageHandler(EGameAction.endGame, (message) {
      onEndGame(message.data);
    });

    _gameSocketService.addMessageHandler(EGameAction.endGameUpdateData,
        (message) {
      onEndGameUpdateData(message.data);
    });

    _gameSocketService.addMessageHandler(EGameAction.failure, (message) {
      onGameFailure(message.data);
    });

    // Handler para quando a conexão é estabelecida
    _gameSocketService.addConnectionHandler(() {
      onConnect();
    });

    // Handler para quando a conexão é perdida
    _gameSocketService.addDisconnectionHandler(() {
      onDisconnect({});
    });
  }

  getNumberOfGamesPlayedByLoggedUser() async {
    final res = await _getNumberOfRoundsOfLoggedPlayerUseCase(NoParams());

    res.fold((left) => {}, (right) {
      _gameStore.setNumberOfGamesPlayedByLoggedUser(right);
      _gameStorage.setNumberOfGamesPlayed(played: right);
    });
  }

  startRestartController(UserEntity? friend,
      {EGameMode? gameMode, int? quizId}) async {
    // EGameMode gMode = EGameMode.fourRandom;
    // int qId = 0;
    // if (gameMode == null || quizId == null) {
    //   final lGameParams = await _gameStorage.getLastGameParams();
    //   if (lGameParams['quiz_id'] != null && lGameParams['game_mode'] != null) {
    //     gMode = lGameParams['game_mode'];
    //     qId = lGameParams['quiz_id'];
    //   }
    // } else {
    //   gMode = gameMode;
    //   qId = quizId;
    // }
    _isGameCancelRequested = false;
    clearData();
    _predeterminedFriend = friend;
    getNumberOfGamesPlayedByLoggedUser();
    initSocket();
    onConnect();
    // _gameMode = gMode;
    // _quizId = qId;
    // _updateUserQuizProgress();
    // if (gameMode != null && quizId != null) {
    //   _gameStore.setGameMode(gMode);
    //   _gameStore.setQuizId(qId);
    //   _gameStorage
    //       .setLastGameParams(params: {"game_mode": gMode, "quiz_id": qId});
    // }
  }

  startRestartControllerBck(UserEntity? friend) {
    clearData();
    _predeterminedFriend = friend;
    getNumberOfGamesPlayedByLoggedUser();
    initSocket();
  }

  // startTimer() => _gameProgressStore.startCurrentTimeCounting();
  bool get isModalBottomSheetOpened => _isModalBottomSheetOpened;
  set isModalBottomSheetOpened(bool value) => _isModalBottomSheetOpened = value;

  void cancelGame() {
    final cancelMessage = getMessage(EGameAction.cancelGame);
    sendMessage(cancelMessage);
  }

  stopSocket() {
    try {
      _gameStorage.setGameRoomWaiting();
      if (isSocketConnected && !_isGameCancelRequested) {
        if (_gameStore.state.gameMode == EGameMode.oneOnInvitedPlayers) {
          _isGameCancelRequested = true;
        }
      }
      onDisconnect({});
      // O GameSocketService não precisa ser desconectado aqui pois ele
      // permanece conectado globalmente
    } catch (e) {
      debugPrint('err-->$e');
    }
  }

  bool get isRoomOwner {
    return getLocalUserIsRoomOwnerFrom(_gameStore.state.room.users);
  }

  bool getLocalUserIsRoomOwnerFrom(List<SocketUserEntity> users) => users
      .firstWhere(
          (element) => element.user.identifier == localPlayer.identifier,
          orElse: () => const SocketUserEntity(
              roomOwner: true, user: UserEntity(name: '', uuid: '')))
      .roomOwner;

  bool get isSocketConnected {
    return _gameSocketService.isConnected;
  }

  getPlayersImages(List<SocketUserEntity> users) async {
    if (_getPlayersImagesAlreadyCalled) return;
    _getPlayersImagesAlreadyCalled = true;
    List<UserImageMemoryData> data = [];
    for (int i = 0; i < users.length; i++) {
      if (users[i].user.avatarUrl != null &&
          users[i].user.avatarUrl!.isNotEmpty) {
        var res =
            await _getImageUint8ListFromUrlUseCase(users[i].user.avatarUrl!);
        res.fold((left) => {print(left)}, (right) {
          data.add(UserImageMemoryData(users[i].user.id, right));
        });
      }
    }
    _gameStore.addFriendsImages(data);
  }

  changeRound() {
    !_gameRoundInfoStore.state.answering
        ? _gameRoundInfoStore.nextRound(
            question: 'Question ${_gameRoundInfoStore.state.currentRound + 1}')
        : _gameRoundInfoStore
            .setAnsweringState(!_gameRoundInfoStore.state.answering);
    _gameProgressStore.restartCurrentTime();
    times++;
    if (times >= 2) {
      _gameProgressStore.stopTimer();
      // Modular.to.pushNamedAndRemoveUntil(AppRoutes.home, (p0) => false);
      // _gameRoundInfoStore.restart();
    }
  }

  setFriendInviteParams(FriendInviteParamsEntity params,
      {UserEntity? friendData}) {
    _friendInviteParamsEntity = params;
    _friendInvitedData = friendData;
  }

  getNewSocket(String token) {
    if (kIsWeb) {
    } else {
      RemoteConfigStore remoteConfigStore = Modular.get<RemoteConfigStore>();
      final socketUrl = AppEnv.mode == EAppRunMode.production
          ? remoteConfigStore.state.productionGameServerUrl
          : remoteConfigStore.state.developmentGameServerUrl;

      return IOWebSocketChannel.connect(socketUrl,
          headers: {"authorization": _userStore.state.token});
    }
  }

  onGameFailure(dynamic data) async {
    if (data is Map && data.containsKey('code')) {
      // String code = data['code'];
      if (data['code'] != 'roomDontAcceptingAnswers') {
        ShowMessage(
            noAvatar: true,
            message: 'The game has been canceled by server',
            onPressed: goHome);
      }
    }
  }

  verifySocketAndStart() async {
    try {
      await channel!.ready;
      onConnect();
    } catch (err) {
      ShowMessage(
          noAvatar: true,
          message: socketConnectionErrorMessage,
          onPressed: goHome);
    }
  }

  _onMessageReceived(String message) {
    final serverMessage = GameMessageDTO.fromJson(message);
    final data = serverMessage.data;
    switch (serverMessage.action) {
      case EGameAction.welcome:
        onWelcome(data);
        break;
      case EGameAction.userEntered:
        setRoomUsers(data);
        break;
      case EGameAction.gameStarting:
        onRoomIsCompleted(data);
        break;
      case EGameAction.gameCancelled:
        onGameCancelled(data);
        break;
      case EGameAction.startRound:
        onStartRound(data);
        break;
      case EGameAction.updateAnswers:
        onUpdateAnswers(data);
        break;
      case EGameAction.updatePontuations:
        onUpdatePontuations(data);
        break;
      case EGameAction.reviewRound:
        onReviewRound(data);
        break;
      case EGameAction.playerReceivePontuation:
        onPlayerReceivePontuation(data);
        break;
      case EGameAction.endGame:
        onEndGame(data);
        break;
      case EGameAction.endGameUpdateData:
        onEndGameUpdateData(data);
        break;
      case EGameAction.failure:
        onGameFailure(data);
        break;
      default:
        print(serverMessage.action);
    }
  }

  GameMessageDTO getMessage(EGameAction action,
      {AbsMappable? data, String? roomName}) {
    return _gameSocketService.createMessage(action,
        data: data, roomName: roomName);
  }

  sendMessage(AbsMappable data) {
    _gameSocketService.sendMessage(data);
  }

  onEndGameUpdateData(dynamic data) async {
    setEndGameData(data);
    final messageData = getMessage(EGameAction.disconnectEndGame);
    sendMessage(messageData);
    _gameProgressStore.setProgressTo(-1);
    // AppEvents.userFinishedGame();
    Analytics.instance.logEvent(name: 'user_finished_game');
    _gameStorage.setGameRoomWaiting();
    stopSocket();
    Modular.to.pushReplacementNamed(AppRoutes.gameEnd());
  }

  configureSocket() {
    channel!.stream.listen(
      (message) {
        _onMessageReceived(message);
      },
      onDone: () {
        print('Connection End');
      },
      onError: (error) {
        ShowMessage(
            noAvatar: true,
            message: socketConnectionErrorMessage,
            onPressed: goHome);
      },
      cancelOnError: true,
    );
  }

  initSocket() async {
    if (isSocketConnected) return;
    _gameStore.clear();

    // O GameSocketService já gerencia a conexão automaticamente
    // quando o usuário está logado, então só precisamos garantir
    // que está conectado
    if (!_gameSocketService.isConnected) {
      await _gameSocketService.connect();
    }

    _gameStore.setAlreadyPlayed(hasLoggedUserEverHadAnyMatches);
  }

  initSocketBck() async {
    if (isSocketConnected) return;
    _gameStore.clear();
    String token = await _userStore.getToken();

    socket = getNewSocket(token);

    socket!.connect();
    configureSocket();
    _gameStore.setAlreadyPlayed(
        hasLoggedUserEverHadAnyMatches); //await _userStorage.getAlreadyPlayed());
  }

  //@Deprecated
  void startGameFunctions() async {
    final roomNameWaiting = await _gameStorage.getGameRoomWaiting();
    if (roomNameWaiting != '') {
      _gameStore.setGameMode(EGameMode.oneOnInvitedPlayers);
      enterGame(addBots: false);
      return;
    }
  }

  bool get hasLoggedUserEverHadAnyMatches =>
      _gameStore.state.numberOfGamesPlayedByLoggedUser > 0;

  void startTimerToStartSocket() {
    _startSocketTimer = setTimeout(
        callback: () {
          enterGame(addBots: true);
        },
        duration: Duration(seconds: hasLoggedUserEverHadAnyMatches ? 5 : 3));
  }

  void stopTimerToStartSocket() {
    if (_startSocketTimer != null) _startSocketTimer!.cancel();
    _startSocketTimer = null;
  }

  startTimeoutStartGame() {
    setTimeout(
        callback: () {
          if (_gameStore.state.gameMode == EGameMode.fourRandom &&
              _gameStore.state.room.users.length < 4) {
            stopSocket();
            _gameStore.setErrorData(const GenericFailure(
                message: 'ALL THE ROOMS ARE FULL. PLEASE TRY AGAIN!'
                //'CURRENTLY, THERE ARE NO AVAILABLE QUYCKY PLAYERS.'
                ));
          }
        },
        duration: const Duration(seconds: 25));
  }

  SocketUserEntity get friendImWaitingToAcceptTheInvitation =>
      _gameStore.state.invitedPlayers.values.firstWhere(
          (element) => element.user.uuid == 'WAITING',
          orElse: () => const SocketUserEntity(user: UserEntity(name: '')));

  bool get asAnyInvitedFriendAlreadyAccepted =>
      _gameStore.state.invitedPlayers.isNotEmpty &&
      _gameStore.state.invitedPlayers.values
              .firstWhere((element) => element.user.uuid != 'WAITING',
                  orElse: () =>
                      const SocketUserEntity(user: UserEntity(name: '')))
              .user
              .name !=
          '';

  bool alreadyInvitedThisFriend(UserEntity? friendData) {
    if (friendData != null) {
      var invitedFriends = _gameStore.state.invitedPlayers;

      for (var key in invitedFriends.keys) {
        if (invitedFriends[key]!.user.id == friendData.id) {
          return true;
        }
      }
      return false;
    }
    return false;
  }

  Future<bool> checkForGameWaitingAndEnterIt() async {
    final roomNameWaiting = await _gameStorage.getGameRoomWaiting();
    if (roomNameWaiting.isNotEmpty) {
      enterGame(roomName: roomNameWaiting);
      _gameStorage.setGameRoomWaiting();
      return true;
    }
    return false;
  }

  void _showMessageEmptyLobby() {
    if (_gameStore.state.gameMode != EGameMode.spotMatch ||
        _gameStore.state.room.users.length > 1 ||
        _emptyLobbyTimer == null) {
      return;
    }
    _emptyLobbyTimer!.cancel();
    _emptyLobbyTimer = null;

    cancelGame();
    ShowGenericDialog(
        widget: ConfirmationDialog(
      title: "EMPTY LOBBY",
      message:
          "Looks like there's no one here right now.\nTry again later or start a random match!",
      primaryActionLabel: "RANDOM MATCH",
      onPrimaryAction: () {
        Navigator.of(AppWidget.globalKey.currentState!.context).pop();
        setGameMode(EGameMode.fourRandom);
        enterGame(addBots: true);
      },
      secondaryActionLabel: "TRY LATER",
      onSecondaryAction: () {
        Navigator.of(AppWidget.globalKey.currentState!.context).pop();
        goHome();
      },
    ));
  }

  enterGame(
      {bool addBots = false, String roomName = '', String spotName = ''}) {
    if (DateTime.now().difference(lastEnterGamer).inSeconds < 5) {
      return;
    }
    lastEnterGamer = DateTime.now();
    _emptyLobbyTimer = null;
    final gameMode = spotName.isNotEmpty
        ? EGameMode.spotMatch
        : _gameFriendInviteStore.state.invitePos > -1
            ? EGameMode.oneOnInvitedPlayers
            : EGameMode.fourRandom;
    // String gameModeName = (_gameFriendInviteStore.state.invitePos > -1
    //     ? EGameMode.oneOnInvitedPlayers.name
    //     : (_gameMode != null ? _gameMode!.name : EGameMode.fourRandom.name));

    // EGameMode gameMode = (_gameFriendInviteStore.state.invitePos > -1
    //     ? EGameMode.oneOnInvitedPlayers
    //     : (_gameMode ?? EGameMode.fourRandom));
    // Map<String, dynamic> gameData = {
    //   'gameMode': gameModeName,
    //   'roomName': roomName,
    //   'quizId': 0,
    //   'userData': _userStore.state.user.toMap()
    // };

    _emptyLobbyTimer ??= setTimeout(
        callback: _showMessageEmptyLobby,
        duration: const Duration(seconds: 30));

    final playerEnteringData = PlayerEnteringGameDTO(
        gameMode: gameMode,
        quizId: 0,
        roomName: roomName,
        userData: _userStore.state.user,
        spotName: spotName);
    // socket!.emit('#playerIn', gameData);

    final data = getMessage(EGameAction.initiate, data: playerEnteringData);
    sendMessage(data);
  }

  void _startGameInvitingFriend() {
    stopTimerToStartSocket();
    _gameStore.setGameMode(EGameMode.oneOnInvitedPlayers);
    setDataOfFriendToInvite(GameFriendInviteEntity(
        invitePos: 1,
        friendInvite: FriendInviteParamsEntity(
            id: _predeterminedFriend!.id,
            friendIdentifier: _predeterminedFriend!.identifier ?? '',
            roomName: ''),
        friendData: _predeterminedFriend!));
    if (alreadyInARoom) {
      inviteFriendFromStoreData();
      return;
    }
    enterGame(addBots: false);
  }

  onConnect() async {
    debugPrint('=============================================');
    debugPrint('Connection established');
    debugPrint('=============================================');

    if (_predeterminedFriend != null) {
      _startGameInvitingFriend();
      return;
    }
    await checkForGameWaitingAndEnterIt();
  }

  startGame([String? spotName]) async {
    // socket!.emit('#startGame');
    final messageData = getMessage(EGameAction.startGame,
        data: StartGameDTO(roomName: _gameStore.state.room.roomName ?? ''));
    sendMessage(messageData);
    _gameStore.clearInvitedPlayers();
  }

  startGameBck() async {
    socket!.emit('#startGame');
    _gameStore.clearInvitedPlayers();
  }

  requestBot() {
    if (isSocketConnected &&
        _gameStore.state.currentState == EGameState.waitingPlayers) {
      final botMessage = getMessage(EGameAction.addBot);
      sendMessage(botMessage);
    }
  }

  requestBots() {
    if (isSocketConnected &&
        _gameStore.state.currentState == EGameState.waitingPlayers) {
      final botsMessage = getMessage(EGameAction.addBots);
      sendMessage(botsMessage);
    }
  }

  void _autoAddBot() {
    if (_gameStore.state.room.users.length <
        _gameStore.state.room.flagMaxPlayers) {
      if (_requestNewBot) {
        requestBot();
      }
      _startAutoAddBot();
      _requestNewBot = true;
    }
  }

  void _startAutoAddBot() {
    setTimeout(callback: _autoAddBot, duration: const Duration(seconds: 3));
  }

  sendAnswer() {
    final questionType = _gameStore
        .state.room.questions[_gameStore.state.room.flagCurrentRound - 1].type;

    if ((questionType == 'direct_answer' &&
            userResponseTextEditController.text.isEmpty) ||
        _gameStore.state.currentState == EGameState.reviewing ||
        (questionType == 'choice' &&
            _gameStore.state.selectedQuestionOption == null)) {
      return;
    }
    PlayerSendAnswerDTO response = PlayerSendAnswerDTO(
        roomName: _gameStore.state.room.roomName ?? '',
        round: _gameStore.state.room.flagCurrentRound,
        answer: userResponseTextEditController.text,
        questionOptionId: _gameStore.state.selectedQuestionOption?.id,
        isCorrectAnswer:
            _gameStore.state.selectedQuestionOption?.isCorrectAnswer);
    response = response.copyWith(
        answer: _gameStore.state.selectedQuestionOption?.value);

    // socket!.emit('#playerTyping', response);
    final message = getMessage(EGameAction.setPlayerAnswer, data: response);
    sendMessage(message);
    setStateReviewing(changeTimer: false, updateRoomState: false);
  }

  calcNewProgressVal(int val) {
    return (1 - (val / _totalTimeAnswering));
  }

  forceStartingGame() {
    if (_gameStore.state.currentState == EGameState.waitingPlayers) {
      startGame();
    }
  }

  onCountDown(dynamic data) {
    int countDownValue = (data['count'] as int);
    _gameCountdownStore.setCountdownTo(countDownValue);
    _gameProgressStore.setProgressTo(calcNewProgressVal(countDownValue),
        autoIncrement: true);
  }

  onDisconnect(dynamic data) {}

  clearData() {
    _predeterminedFriend = null;
    _gameStore.clear();
    _gameFriendInviteStore.clear();
    userResponseTextEditController.text = '';
    _friendshipStore.clear();
    _requestNewBot = true;
    _gameRoundInfoStore.clear();
    _gameCountdownStore.clear();
    _gameProgressStore.clear();
    userResponseTextEditController.text = '';
    alreadyGavePoint = false;
    times = 0;
    _friendInviteParamsEntity =
        FriendInviteParamsEntity(id: '', friendIdentifier: '', roomName: '');
    _friendInvitedData = null;
  }

  bool get alreadyInARoom => _gameStore.state.room.roomName!.isNotEmpty;

  SocketUserEntity getLocalSocketUserFromRoomUsers(RoomEntity room) {
    return room.users.firstWhere(
        (element) => element.user.identifier == localPlayer.identifier);
  }

  void inviteFriendFromStoreData() {
    if (_gameFriendInviteStore.state.invitePos > -1) {
      addInvitedUser(_gameFriendInviteStore.state);
      _gameFriendInviteStore.clear();
      return;
    }
  }

  onWelcome(dynamic data) {
    try {
      RoomEntity room = RoomEntity.fromMap(data);
      setRoomData(room);
      _gameStore.setGameMode(room.gameMode);

      inviteFriendFromStoreData();

      if (EGameMode.fourRandom == room.gameMode) {
        _gameStorage.getGameRoomWaiting().then((value) {
          if (value.isEmpty) startTimeoutStartGame();
        });
        if (getLocalSocketUserFromRoomUsers(room).roomOwner) {
          _startAutoAddBot();
          // requestBots();
        }
      }
      // var userSocketData =
      room.users
          .firstWhere((v) => (v.user.identifier == localPlayer.identifier));
      _gameStore.setIsRoomOwner(getLocalUserIsRoomOwnerFrom(room.users));
    } catch (e) {
      debugPrint('Err:GC=>$e');
    }
    // if (room.answers && room.answers.length > 0) {
    //   onStartRound(room);
    // } else {
    //   onWelcome?.(room);
    // }
    // this.setRoomData(room);
  }

  onRoomIsCompleted(dynamic data) {
    setStateStarting();
    // setRoomData(data);

    _gameProgressStore.setFractionOfTime(1 / (_totalTimeAnswering * 10));
    // _gameCountdownStore.setCountdownTo(5);

    // Enviar mensagem de player ready através do GameSocketService
    final readyMessage = getMessage(
        EGameAction.connect); // ou uma ação específica para player ready
    sendMessage(readyMessage);
  }

  onGameCancelled([dynamic data]) {
    // RoomEntity room = data is RoomEntity ? data : RoomEntity.fromMap(data);

    if (!_isGameCancelRequested) {
      stopSocket();
      ShowMessage(
          noAvatar: true,
          message: 'The game has been canceled',
          onPressed: goHome);
    }
  }

  getUserData() async {
    UserController userController = Modular.get<UserController>();

    await _userStore.getLoginResultData();
    final user = await userController.getUserById(_userStore.state.user.id);
    if (user != null) {
      _userStore.setUser(user);
    }
  }

  void goHome() {
    Modular.to.pushNamedAndRemoveUntil(AppRoutes.home, (p0) => false);
  }

  void setGameMode(EGameMode gameMode) {
    _gameStore.setGameMode(gameMode);
  }

  setGameStoreInfo(RoomEntity room, bool answering) {
    _gameRoundInfoStore.setGameRoundInfo(GameRoundInfoEntity(
        currentQuestion: room.questions[room.flagCurrentRound - 1].description,
        currentRound: room.flagCurrentRound,
        totalRounds: _gameStore.state.room.numRounds,
        answering: answering));
  }

  onStartRound(dynamic data) {
    //RoomEntity room = RoomEntity.fromMap(data);
    //_gameStore.setRoomData(room);
    //setGameStoreInfo(room, true);
    //alreadyGavePoint = false;
    // if (this.currentAnswer != 'reviewing') this.onStartRound(room);
    //this.currentState = 'answering';
    // setRoomData(data);
    // setStateAnswering();
    // RoomEntity room = data is RoomEntity ? data : RoomEntity.fromMap(data);

    setStateAnswering();

    if (_gameStore.state.room.flagCurrentRound == 1) {
      Analytics.instance.logEvent(name: 'play_game_start');
    }
    userResponseTextEditController.text = '';
    _gameStore.clearReceivedPontuations();
    _gameStore.setAlreadyGavePoint(false);
    _gameStore.setShowPointButtons(true);

    if (Modular.to.path == AppRoutes.gameLobby()) {
      Modular.to.pushReplacementNamed(AppRoutes.gamePlay());
    }
  }

  onEndGame(dynamic data) async {
    setRoomData(data);
    // currentState = 'end-game-review';
    // this.onEndGame(room);

    // Enviar mensagem de disconnect end game através do GameSocketService
    final disconnectMessage = getMessage(EGameAction.disconnectEndGame);
    sendMessage(disconnectMessage);

    _gameProgressStore.setProgressTo(-1);
    // RemoteConfigStore remoteConfigStore = Modular.get<RemoteConfigStore>();
    // if (!hasLoggedUserEverHadAnyMatches) {
    //   _userStorage.setAlreadyPlayed(true);
    //   _promotionStore.addPromotion(PromotionEntity(
    //       identifier: '_first_end_game',
    //       name: 'First End Game Promotion',
    //       url: remoteConfigStore.state.promoUrlEndgame,
    //       startTime: DateTime.now()));
    // }
    final currentPlayedGamesNumber =
        _gameStore.state.numberOfGamesPlayedByLoggedUser + 1;

    if (currentPlayedGamesNumber == 5) {
      Analytics.instance.logEvent(name: 'play_complete_5');
    }

    Modular.to.pushReplacementNamed(AppRoutes.gameEnd());
    Analytics.instance.logEvent(name: 'play_game_end');
    _gameStorage.setGameRoomWaiting();
  }

  onReviewRound(dynamic data) {
    sendAnswer();
    // setRoomData(data);
    setStateReviewing(changeTimer: true);
    //currentState = 'reviewing';
  }

  onPlayerReceivePontuation(dynamic data) {
    PontuationEntity? pontuationData;
    try {
      pontuationData = PontuationEntity.fromMap(data);
      if (pontuationData.userThatReceivePontuationIdentifier ==
          localPlayer.identifier) {
        _gameStore.addReceivedPontuations(pontuationData);
        return;
      }
      _gameStore.addRoomPontuation(pontuationData);
    } catch (e) {
      debugPrint('Err->$e');
    }

    // setRoomData(data['room']);
  }

  removeUser(dynamic data) {}

  onUpdateAnswers(dynamic data) {
    try {
      List<AnswerEntity> listAnswers = List<AnswerEntity>.from(
        (data as List<dynamic>).map<AnswerEntity>(
          (x) => AnswerEntity.fromMap(x as Map<String, dynamic>),
        ),
      );
      _gameStore.setAnswers(listAnswers);
    } catch (e) {
      debugPrint('error:=>$e');
    }
  }

  onUpdatePontuations(dynamic data) {
    try {
      List<PontuationEntity> listPontuations = List<PontuationEntity>.from(
        (data as List<dynamic>).map<PontuationEntity>(
          (x) => PontuationEntity.fromMap(x as Map<String, dynamic>),
        ),
      );
      _gameStore.setPontuations(listPontuations);
    } catch (e) {
      debugPrint('error:=>$e');
    }
  }

  setEndGameData(dynamic data) {
    // EndGameEntity endGameData =
    //     data is EndGameEntity ? data : EndGameEntity.fromMap(data);
    // _endGameStore.setData(endGameData);
  }

  setRoomData(dynamic data) {
    RoomEntity room = data is RoomEntity ? data : RoomEntity.fromMap(data);
    room = room.copyWith(users: _filterUsers(room.users));
    // // addNewUser(room.users);
    _gameStore.setRoomData(room);

    if (room.gameState != EGameState.waitingPlayers &&
        _gameStore.state.friendsImages.isEmpty) {
      getPlayersImages(room.users);
    }
    // setUserRoomOwner(room.roomName ?? '');
  }

  setUserRoomOwner(String roomName) async {
    String gameRoomWaiting = await _gameStorage.getGameRoomWaiting();
    if (isRoomOwner && gameRoomWaiting.isEmpty) {
      _gameStorage.setGameRoomWaiting(roomName: roomName);
    }
  }

  inviteFriendToPlay(FriendInviteParamsEntity friendInvite) {
    _inviteFriendToPlayUseCase(friendInvite);
  }

  Future<bool> addFriendImmediately(String invitedUserId) async {
    bool res = false;
    final user = localPlayer;

    String userId = user.id;
    final result = await _AddPlayerAsFriendImmediatelyUseCase(
        InviteRequest(userId: userId, invitedUserId: invitedUserId));
    result.fold((left) => {print(left)}, (right) {
      res = true;
    });
    return res;
  }

  int indexUserInvitedByIdentifier(String identifier) {
    int res = -1;
    for (int key in _gameStore.state.invitedPlayers.keys) {
      if (_gameStore.state.invitedPlayers[key]!.user.identifier == identifier) {
        return key;
      }
    }
    return res;
  }

  addInvitedPlayer(int pos, SocketUserEntity socketUser) {
    _gameStore.addInvitedPlayer(pos, socketUser);
    if (_gameStore.state.invitedPlayers.length == 1) {
      setTimeout(
          callback: () => _gameStore.setShowAllInviteSlots(true),
          duration: const Duration(seconds: 1)); //--Change
    }
  }

  bool get hasUsersOnRoom => _gameStore.state.room.users.where((element) {
        return element.user.identifier != localPlayer.identifier &&
            element.user.identifier != '_' &&
            element.user.uuid != '_';
      }).isNotEmpty;

  bool isPlayerAFriend(String identifier) {
    for (int i = 0; i < _friendshipStore.state.friendships.length; i++) {
      if ((_friendshipStore.state.friendships[i].friendUserId !=
                  localPlayer.id &&
              _friendshipStore.state.friendships[i].friendUser.identifier ==
                  identifier) ||
          (_friendshipStore.state.friendships[i].userId != localPlayer.id &&
              _friendshipStore.state.friendships[i].user.identifier ==
                  identifier)) {
        return true;
      }
    }
    return false;
  }

  List<SocketUserEntity> _filterUsers(List<SocketUserEntity> value) {
    List<SocketUserEntity> roomUsers = [];
    for (var item in value) {
      if (roomUsers.indexWhere(
              (element) => element.user.identifier == item.user.identifier) ==
          -1) {
        roomUsers.add(item);
      }
    }
    return roomUsers;
  }

  setRoomUsers(dynamic data) {
    List<SocketUserEntity> tempRoomUsers = List<SocketUserEntity>.from(
      (data as List<dynamic>).map<SocketUserEntity>(
        (x) => SocketUserEntity.fromMap(x),
      ),
    );
    List<SocketUserEntity> roomUsers = _filterUsers(tempRoomUsers);
    // for (var element in roomUsers) {
    //   print('Log==>${element.user.name}');
    // }
    if (_gameStore.state.invitedPlayers.isNotEmpty &&
        _gameStore.state.gameMode == EGameMode.oneOnInvitedPlayers) {
      int index = 0;
      for (var element in roomUsers) {
        if (element.user.identifier != localPlayer.identifier) {
          index = indexUserInvitedByIdentifier(element.user.identifier ?? '');
          index = index > -1 ? index : indexUserInvitedByIdentifier('_');
          if (index > -1) {
            _gameStore.changeInvitedUser(index, element);
          }
          if (element.user.identifier != null &&
              getLocalUserIsRoomOwnerFrom(roomUsers) &&
              !isPlayerAFriend(element.user.identifier!)) {
            addFriendImmediately(element.user.id.toString());
          }
        }
      }
      if ((_gameStore.state.invitedPlayers.length >= 3 &&
              roomUsers.length > 3) ||
          (_predeterminedFriend != null && roomUsers.length == 2)) {
        _gameStore.setGameStartStage(EGameStartStage.preparing);
        setTimeout(
            callback: () {
              _gameStore.setGameStartStage(EGameStartStage.starting);
              setTimeout(
                  callback: startGame, duration: const Duration(seconds: 5));
            },
            duration: Duration(seconds: 5));
      }
    } else if (_gameStore.state.gameMode == EGameMode.fourRandom) {
      _requestNewBot = false;
      if (roomUsers.length >= _gameStore.state.room.flagMaxPlayers) {
        setTimeout(
            callback: forceStartingGame, duration: const Duration(seconds: 5));
      }
    }
    _gameStore.setRoomUsers(roomUsers);
  }

  addInvitedPlayersToRoom() {
    List<SocketUserEntity> roomUsers = _gameStore.state.room.users;
    for (var key in _gameStore.state.invitedPlayers.keys) {
      int c = roomUsers.indexWhere((roomUser) =>
          _gameStore.state.invitedPlayers[key]!.user.identifier ==
              roomUser.user.identifier &&
          _gameStore.state.invitedPlayers[key]!.user.uuid ==
              roomUser.user.uuid &&
          _gameStore.state.invitedPlayers[key]!.user.name ==
              roomUser.user.name);
      if (c == -1) roomUsers.add(_gameStore.state.invitedPlayers[key]!);
    }
    _gameStore.setRoomUsers(roomUsers);
  }

  finishRound() {
    // Enviar mensagem de player finished round através do GameSocketService
    // Pode precisar criar uma nova ação ou usar uma existente
    final finishMessage =
        getMessage(EGameAction.disconnect); // ou uma ação específica
    sendMessage(finishMessage);
  }

  givePointToPlayer(
      String userIndentifier, EGamePontuationType pontuationType) {
    final dto = PlayerSendPontuationDto(
        roomName: _gameStore.state.room.roomName ?? '',
        round: _gameStore.state.room.flagCurrentRound,
        userThatSetPontuationIdentifier:
            localPlayer.identifier ?? '', // Garantir que não seja nulo
        userThatReceivePontuationIdentifier: userIndentifier,
        pontuation: pontuationType == EGamePontuationType.fire ? 10 : 5);
    final messageData = getMessage(EGameAction.setPlayerPontuation, data: dto);
    sendMessage(messageData);

    Analytics.instance.logEvent(
        name: pontuationType == EGamePontuationType.cold
            ? 'play_give_ice'
            : 'play_give_flame');

    _gameStore.setAlreadyGavePoint(true,
        userWhoReceivedPoint: userIndentifier, pontuationType: pontuationType);
  }

  hidePointButtons() {
    _gameStore.setShowPointButtons(false);
  }

  setSelectedQuestionOption(QuestionOptionEntity? selectedOption) {
    if (selectedOption != null) {
      userResponseTextEditController.text = "";
    }
    _gameStore.setSelectedQuestionOption(selectedOption);
  }

  setStateAnswering() {
    try {
      _gameStore.setSelectedQuestionOption(null);
      userResponseTextEditController.text = '';
      final int flagCurrentRound = _gameStore.state.room.numRounds >
              _gameStore.state.room.flagCurrentRound
          ? _gameStore.state.room.flagCurrentRound + 1
          : _gameStore.state.room.flagCurrentRound;
      setState(EGameState.answering,
          flagCurrentRound: flagCurrentRound,
          stateTimeInSeconds:
              _gameStore.state.room.questions[flagCurrentRound - 1].type ==
                      'choice'
                  ? _gameStore.state.room.timeAnsweringQuestionMultiple
                  : _gameStore.state.room.timeAnsweringQuestionDirect);
      // _gameStore.setCurrentState(EGameState.answering);

      if (_timesWritingRoomName == 0) {
        _gameStorage.setGameRoomWaiting(
            roomName: _gameStore.state.room.roomName ?? '');
        _timesWritingRoomName++;
      }
    } catch (err) {}
  }

  setStateWaitingPlayers() {
    _gameStore.setCurrentState(EGameState.waitingPlayers);
  }

  setStateStarting() {
    setState(EGameState.starting,
        stateTimeInSeconds: _gameStore.state.room.timeWaitingStartingGame);
    //_gameStore.setCurrentState(EGameState.starting);
  }

  setStateReviewing({changeTimer = false, bool updateRoomState = true}) {
    setState(EGameState.reviewing,
        updateRoomState: updateRoomState,
        stateTimeInSeconds:
            changeTimer ? _gameStore.state.room.timeReviewingRound : null);
    print(
        'setStateReviewing==>${_gameStore.state.room.answers[_gameStore.state.room.flagCurrentRound - 1].answer}');
  }

  setStateEndGameReview() {
    setState(EGameState.endGameReview);
    // _gameStore.setCurrentState(EGameState.endGameReview);
  }

  setState(EGameState newState,
      {int? stateTimeInSeconds,
      bool updateRoomState = true,
      int? flagCurrentRound}) {
    final currentRound =
        flagCurrentRound ?? _gameStore.state.room.flagCurrentRound;
    final newTime =
        stateTimeInSeconds ?? _gameStore.state.room.stateTimeInSeconds;

    setRoomData(_gameStore.state.room.copyWith(
        gameState: updateRoomState ? newState : null,
        flagCurrentRound: currentRound,
        stateTimeInSeconds: newTime));

    _gameStore.setCurrentState(newState);
    // _gameStore.setCurrentState(newState);
  }

  int getGameTotalScore() {
    int total = 0;
    for (var pontuationItem in _gameStore.state.room.pontuations) {
      total += pontuationItem.pontuation;
    }
    return total;
  }

  int getPlayerTotalPointsByIdentifier(String identifier) {
    int total = 0;
    for (var pontuationItem in _gameStore.state.room.pontuations) {
      if (pontuationItem.userThatReceivePontuationIdentifier == identifier) {
        total += pontuationItem.pontuation;
      }
    }
    return total;
  }

  int getPlayerTotalFiresByIdentifier(String identifier) {
    int total = 0;
    for (var pontuationItem in _gameStore.state.room.pontuations) {
      if (pontuationItem.userThatReceivePontuationIdentifier == identifier &&
          pontuationItem.currentRound ==
              _gameStore.state.room.flagCurrentRound &&
          pontuationItem.pontuation == 10) {
        total++;
      }
    }
    return total;
  }

  int getPlayerRoomRankingByIdentifier(String identifier) {
    return rankedPlayers
            .indexWhere((element) => identifier == element.user.identifier) +
        1;
  }

  String getPlayerCurrentAnswerByIdentifier(String identifier) {
    int currentRoundIndex = _gameStore.state.room.flagCurrentRound;
    for (var element in _gameStore.state.room.answers) {
      if ((element.currentRound == currentRoundIndex) &&
          (element.userIndentifier == identifier)) {
        return element.answer;
      }
    }
    return '';
  }

  List<SocketUserEntity> get players {
    return _gameStore.state.room.users;
  }

  List<SocketUserEntity> get rankedPlayers {
    final withPontuation = players.map((e) {
      return {
        "points": getPlayerTotalPointsByIdentifier(e.user.identifier ?? ''),
        "data": e
      };
    }).toList();
    withPontuation
        .sort((a, b) => (b["points"] as int).compareTo(a["points"] as int));
    return withPontuation.map((e) => e['data'] as SocketUserEntity).toList();
  }

  List<SocketUserEntity> get playersWithoutLocalPlayer {
    return [
      for (SocketUserEntity su in _gameStore.state.room.users)
        if (su.user.identifier != localPlayer.identifier) su
    ];
  }

  void addInvitedUser(GameFriendInviteEntity data) {
    if (_gameStore.state.gameMode == EGameMode.oneOnInvitedPlayers &&
        !alreadyInvitedThisFriend(data.friendData)) {
      if (data.friendInvite!.friendIdentifier == 'share') {
        final message =
            "${POSSIBLE_SHARED_INVITE_MESSAGES[(DateTime.now().millisecondsSinceEpoch % POSSIBLE_SHARED_INVITE_MESSAGES.length).toInt()]}\nhttps://app.quycky.co/game/invite/${_gameStore.state.room.roomName}";
        //'Hey! Let\'s play Quycky '
        Share.share(message)
            .then((value) => {
                  addInvitedPlayer(
                      data.invitePos,
                      const SocketUserEntity(
                          user: UserEntity(
                              identifier: '_',
                              uuid: 'WAITING',
                              name: 'INVITED BY LINK')))
                })
            .catchError((err) {
          debugPrint('Share error: $err');
          return <dynamic>{};
        });
      } else {
        inviteFriendToPlay(data.friendInvite!
            .copyWith(roomName: _gameStore.state.room.roomName));
        addInvitedPlayer(data.invitePos,
            SocketUserEntity(user: data.friendData!.copyWith(uuid: 'WAITING')));
        //  _controller.addInvitedPlayersToRoom();
      }
    }
  }

  UserEntity get localPlayer => _userStore.state.user; //_userStore.state.user;

  List<UserEntity> get friends {
    List<UserEntity> res = [];
    for (FriendshipEntity friend in _friendshipStore.state.friendships) {
      if (friend.situation == 'active') {
        UserEntity friendData =
            (friend.friendUser.identifier == localPlayer.identifier
                ? friend.user
                : friend.friendUser);
        res.add(friendData);
      }
    }
    return res;
  }

  void setDataOfFriendToInvite(GameFriendInviteEntity data) {
    _gameFriendInviteStore.setData(data);
  }
  // const getTotalSumPointsByUserName = (userName = string) => {
  //   let res = 0;
  //   gameStore.roomData.pontuations?.forEach((pontuation) => {
  //     if (userName == pontuation.userThatReceivePontuationIdentifier)
  //       res += pontuation.pontuation;
  //   });
  //   return res;
  // };
  // addNewUser(users?: ISocketUser[]) {
  //   if (users) {
  //     users.forEach((user) => {
  //       console.log(
  //         '==>ABC=>',
  //         this.players.includes(user)
  //       );
  //       if (
  //         user.userName != this.userName &&
  //         !this.players.find((userData) => userData.userName == user.userName)
  //       ) {
  //         this.players.push(user);
  //       }
  //     });
  //   }
  // };
}
