import 'dart:io';

import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:flutter/material.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/game/domain/entities/game_entity.dart';
import 'package:quycky/app/features/game/presenter/controllers/game_controller.dart';
import 'package:quycky/app/features/game/presenter/pages/widgets/game_players_review_section.dart';
import 'package:quycky/app/features/game/presenter/storage/game_storage.dart';
import 'package:quycky/app/features/game/presenter/store/game_round_info_store.dart';
import 'package:quycky/app/features/game/presenter/store/game_store.dart';
import 'package:quycky/app/features/home/<USER>/controllers/question_temperature_slider_store.dart';
import 'package:quycky/app/features/home/<USER>/store/promotion_store.dart';
import 'package:quycky/app/services/remote_config/remote_config_entity.dart';
import 'package:quycky/app/services/remote_config/remote_config_store.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/core/entities/background_opacities_entity.dart';
import 'package:quycky/core/entities/promotion_entity.dart';
import 'package:quycky/core/utils/app_env.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/background_utils.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/core/utils/show_promotional_banner.dart';
import 'package:sizer/sizer.dart';

class GameEndPage extends StatefulWidget {
  final String title;

  const GameEndPage({super.key, this.title = 'GameEndPage'});

  @override
  GameEndPageState createState() => GameEndPageState();
}

class GameEndPageState extends State<GameEndPage> {
  late final BackgroundOpacitiesEntity _backgroundOpacities;
  final _gameController = Modular.get<GameController>();
  final _store = Modular.get<GameRoundInfoStore>();
  final _gameStore = Modular.get<GameStore>();
  final userResponseTextEditController = TextEditingController();
  final _remoteConfigStore = Modular.get<RemoteConfigStore>();
  final ScrollController _scrollController = ScrollController();
  final Key _scrollGameKey = const ValueKey<String>('_scrollGame');
  final _gameStorage = Modular.get<GameStorage>();

  // final Completer<WebViewController> _controller =
  //     Completer<WebViewController>();

  @override
  void initState() {
    super.initState();
    _gameController.stopSocket();
    QuestionTemperatureSliderStore questionTemperatureSliderStore =
        Modular.get<QuestionTemperatureSliderStore>();

    _backgroundOpacities = getBackgroundOpacitiesByQuestionTemperature(
        questionTemperatureSliderStore.state);
    _verifyRulesAndPlay();
  }

  void _verifyRulesAndPlay() {
    if (Platform.isAndroid &&
        _remoteConfigStore.state.popupDisallowedVersions.isNotEmpty &&
        _remoteConfigStore.state.popupDisallowedVersions
            .contains(AppEnv.appVersion)) {
      return;
    }
    final currentPlayedGamesNumber =
        _gameStore.state.numberOfGamesPlayedByLoggedUser + 1;

    _gameStorage.setNumberOfGamesPlayed(played: currentPlayedGamesNumber);

    final bannerToShow =
        _remoteConfigStore.state.bannersPromoRulesAndData.firstWhere(
      (element) => element.whenUserHasPlayed == currentPlayedGamesNumber,
      orElse: () =>
          BannerPromoRuleAndData(whenUserHasPlayed: -1, url: '', imageUrl: ''),
    );
    if (bannerToShow.whenUserHasPlayed > -1) {
      final promotionStore = Modular.get<PromotionStore>();
      promotionStore.addPromotion(PromotionEntity(
          identifier: bannerToShow.whenUserHasPlayed.toString(),
          name: 'Promotion',
          url: bannerToShow.url,
          status: 'waiting',
          startTime: DateTime.now(),
          minutesToClose: bannerToShow.minutesToRedeem));
      setTimeout(
          callback: () => ShowPromotionalBanner(
              banner: bannerToShow,
              url: bannerToShow.url,
              imageUrl: bannerToShow.imageUrl),
          duration: const Duration(seconds: 1));
    }
  }

  Widget animatedSwitch(Widget child) => AnimatedSwitcher(
        duration: const Duration(milliseconds: 400),
        child: child,
      );

  Widget getBodyWidgets() {
    bool isLessOrEqualsToMinimumHeight =
        MediaQuery.of(context).size.height <= AppEnv.minimumLayoutHeight;
    return Container(
      padding: const EdgeInsets.only(top: 25, left: 30, right: 30),
      constraints: const BoxConstraints(maxWidth: 500),
      child: Column(
        children: [
          Stack(
            alignment: AlignmentDirectional.bottomCenter,
            children: [
              // const SizedBox(height: 100),
              Column(
                children: [
                  Container(
                    height: isLessOrEqualsToMinimumHeight ? 35 : 50,
                    decoration: const BoxDecoration(
                        color: CustomColors.orangeSoda90,
                        borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(30),
                            topRight: Radius.circular(30))),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 18.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'QUYCKIES WON',
                            style: TextStyle(
                                letterSpacing: 3,
                                fontFamily: "Roboto",
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                fontSize: 10),
                          ),
                          Row(
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.only(top: 6, right: 12.0),
                                child: Text(
                                  _gameController
                                      .getPlayerTotalPointsByIdentifier(
                                          _gameController
                                                  .localPlayer.identifier ??
                                              '')
                                      .toString(),
                                  style: const TextStyle(
                                      letterSpacing: 3,
                                      fontFamily: "Roboto",
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      fontSize: 10),
                                ),
                              ),
                              Image.asset(
                                'assets/img/png/fire.png',
                                width: 20,
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                  Container(
                    height: isLessOrEqualsToMinimumHeight ? 35 : 50,
                    decoration: const BoxDecoration(
                        color: CustomColors.orangeSoda40,
                        borderRadius: BorderRadius.only(
                            bottomLeft: Radius.circular(30),
                            bottomRight: Radius.circular(30))),
                    child: Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 18.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          const Text(
                            'TOTAL RANK',
                            style: TextStyle(
                                letterSpacing: 3,
                                fontFamily: "Roboto",
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                                fontSize: 10),
                          ),
                          Row(
                            children: [
                              Padding(
                                padding:
                                    const EdgeInsets.only(top: 6, right: 12.0),
                                child: Text(
                                  _gameController
                                      .getPlayerRoomRankingByIdentifier(
                                          _gameController
                                                  .localPlayer.identifier ??
                                              '')
                                      .toString(),
                                  style: const TextStyle(
                                      letterSpacing: 3,
                                      fontFamily: "Roboto",
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                      fontSize: 10),
                                ),
                              ),
                              Image.asset(
                                'assets/img/png/fire.png',
                                width: 20,
                              ),
                            ],
                          )
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
          Padding(
            padding: EdgeInsets.symmetric(
                vertical: isLessOrEqualsToMinimumHeight ? 19 : 38.0),
            child: const Text(
              'GAME CHART SCORE',
              style: TextStyle(
                  letterSpacing: 3,
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 10),
            ),
          ),
          ScopedBuilder.transition(
            store: _gameStore,
            transition: (_, child) {
              return animatedSwitch(child);
            },
            onState: (_, GameEntity state) =>
                animatedSwitch(GamePlayerReviewSection(
              getPlayerTotalPointsByIdentifier:
                  _gameController.getPlayerTotalPointsByIdentifier,
              players: _gameController.playersWithoutLocalPlayer,
              getPlayerImageMemoryData: _gameStore.getFriendImageDataById,
              localPlayer: _gameController.localPlayer,
              rankedPlayers: _gameController.rankedPlayers,
            )),
          ),
        ],
      ),
    );
  }

  _goToQrCodePage() {
    _gameController.clearData();
    Modular.to.pushReplacementNamed(AppRoutes.readGameQrCode());
  }

  _goToLobby() {
    Analytics.instance.logEvent(name: 'play_again_click');
    Modular.to.pushNamedAndRemoveUntil(
        AppRoutes.gameLobby(),
        arguments: {
          'keepPlaying': true,
        },
        (p0) => false);
  }

  _handleKeepPlaying() {
    if (_gameStore.state.gameMode == EGameMode.spotMatch) {
      _goToQrCodePage();
    } else {
      _goToLobby();
    }
  }

  handleBack() =>
      Modular.to.pushNamedAndRemoveUntil(AppRoutes.home, (p0) => false);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientContainer(
        useDefault: true,
        coldOpacity: 0,
        normalOpacity: 0,
        hotOpacity: 0,
        child: SafeArea(
            child: ScopedBuilder(
          onError: (context, error) => Center(
            child: Text(
              'An error occurred, try again later.',
              style: Theme.of(context)
                  .textTheme
                  .bodySmall
                  ?.copyWith(color: Colors.white),
            ),
          ),
          store: _store,
          onState: (context, state) => Padding(
            padding: const EdgeInsets.only(top: 14.0),
            child: Column(
              mainAxisSize: MainAxisSize.max,
              children: [
                AppHeader(
                  title: 'CONGRATULATIONS!',
                  subtitle: 'YOUR SCORE',
                  logoSectionLeftWidget: IconButton(
                      onPressed: handleBack,
                      icon: const Icon(
                        QuyckyIcons.arrow_left_circle,
                        color: Colors.white,
                        size: 23,
                      )),
                ),
                getBodyWidgets(),
                Spacer(),
                Container(
                    margin: const EdgeInsets.only(top: 27),
                    width: 90.w,
                    height: 6.h,
                    child: Button(
                        autoSized: true,
                        onPressed: _handleKeepPlaying,
                        text: 'KEEP PLAYING')),
                Container(
                    margin: EdgeInsets.only(top: 1.18.h),
                    width: 90.w,
                    height: 6.h,
                    child: Button(
                        autoSized: true,
                        outlined: true,
                        borderColor: Colors.white,
                        onPressed: handleBack,
                        text: 'BACK TO HOME')),
                SizedBox(
                  height: 4.h,
                )
              ],
            ),
          ),
        )),
      ),
    );
  }
}
