import 'package:quycky/app/features/game/data/datasources/game_datasource.dart';
import 'package:dartz/dartz.dart';
import 'package:quycky/app/features/game/data/models/game_invitation_model.dart';
import 'package:quycky/app/features/game/domain/entities/friend_invite_params_entity.dart';
import 'package:quycky/app/features/game/domain/entities/friend_invite_response_entity.dart';
import 'package:quycky/app/features/game/domain/repositories/game_repository.dart';
import 'package:quycky/core/usecase/errors/failure.dart';

class GameRepositoryImplementation implements IGameRepository {
  final IGameDatasource datasource;

  GameRepositoryImplementation(this.datasource);

  @override
  Future<Either<Failure, FriendInviteResponseEntity>> inviteFriendToPlay(
      FriendInviteParamsEntity params) async {
    try {
      final result = await datasource.inviteFriendToPlay(params);
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }

  @override
  Future<Either<Failure, List<GameInvitationResponseModel>>>
      getGameInvitationsByUserId() async {
    try {
      final result = await datasource.getGameInvitationsByUserId();
      return Right(result);
    } on Exception {
      return const Left(ServerFailure());
    }
  }
}
