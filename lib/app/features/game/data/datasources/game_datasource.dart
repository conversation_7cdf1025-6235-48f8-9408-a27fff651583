import 'package:quycky/app/features/game/data/models/friend_invite_response_model.dart';
import 'package:quycky/app/features/game/data/models/game_invitation_model.dart';
import 'package:quycky/app/features/game/domain/entities/friend_invite_params_entity.dart';

abstract class IGameDatasource {
  Future<FriendInviteResponseModel> inviteFriendToPlay(
      FriendInviteParamsEntity params);
  Future<List<GameInvitationResponseModel>> getGameInvitationsByUserId();
}
