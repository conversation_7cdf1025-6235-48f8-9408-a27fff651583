// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:quycky/core/utils/type_converters.dart';

class GameInvitationResponseModel {
  final String id;
  final String notificationType;
  final int userId;
  final int friendId;
  final String roomId;
  final String roomName;
  final DateTime createdAt;
  GameInvitationResponseModel({
    required this.id,
    required this.notificationType,
    required this.userId,
    required this.friendId,
    required this.roomId,
    required this.roomName,
    required this.createdAt,
  });

  GameInvitationResponseModel copyWith({
    String? id,
    String? notificationType,
    int? userId,
    int? friendId,
    String? roomId,
    String? roomName,
    DateTime? createdAt,
  }) {
    return GameInvitationResponseModel(
      id: id ?? this.id,
      notificationType: notificationType ?? this.notificationType,
      userId: userId ?? this.userId,
      friendId: friendId ?? this.friendId,
      roomId: roomId ?? this.roomId,
      roomName: roomName ?? this.roomName,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() {
    return <String, dynamic>{
      'id': id,
      'notification_type': notificationType,
      'user_id': userId,
      'friend_id': friendId,
      'room_id': roomId,
      'room_name': roomName,
      'created_at': createdAt.millisecondsSinceEpoch,
    };
  }

  factory GameInvitationResponseModel.fromJson(Map<String, dynamic> map) {
    return GameInvitationResponseModel(
      id: dynamicToString(map['id']),
      notificationType: dynamicToString(map['notification_type']),
      userId: dynamicToInt(map['user_id']),
      friendId: dynamicToInt(map['friend_id']),
      roomId: dynamicToString(map['room_id']),
      roomName: dynamicToString(map['room_name']),
      createdAt: dynamicToDateTime(map['created_at']),
    );
  }

  String toJson() => json.encode(toMap());

  @override
  String toString() {
    return 'GameInvitationResponseModel(id: $id, notificationType: $notificationType, userId: $userId, friendId: $friendId, roomId: $roomId, roomName: $roomName, createdAt: $createdAt)';
  }

  @override
  bool operator ==(covariant GameInvitationResponseModel other) {
    if (identical(this, other)) return true;

    return other.id == id &&
        other.notificationType == notificationType &&
        other.userId == userId &&
        other.friendId == friendId &&
        other.roomId == roomId &&
        other.roomName == roomName &&
        other.createdAt == createdAt;
  }

  @override
  int get hashCode {
    return id.hashCode ^
        notificationType.hashCode ^
        userId.hashCode ^
        friendId.hashCode ^
        roomId.hashCode ^
        roomName.hashCode ^
        createdAt.hashCode;
  }
}
