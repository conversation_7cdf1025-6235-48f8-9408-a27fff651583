// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:convert';

import 'package:quycky/app/features/game/domain/entities/game_entity.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/core/entities/abs_mappable.dart';

class PlayerEnteringGameDTO implements AbsMappable {
  final EGameMode gameMode;
  final int quizId;
  final String roomName;
  final UserEntity userData;
  final String? spotName;

  PlayerEnteringGameDTO({
    required this.gameMode,
    required this.quizId,
    required this.roomName,
    required this.userData,
    this.spotName,
  });

  PlayerEnteringGameDTO copyWith({
    EGameMode? gameMode,
    int? quizId,
    String? roomName,
    UserEntity? userData,
    String? spotName,
  }) {
    return PlayerEnteringGameDTO(
      gameMode: gameMode ?? this.gameMode,
      quizId: quizId ?? this.quizId,
      roomName: roomName ?? this.roomName,
      userData: userData ?? this.userData,
      spotName: spotName ?? this.spotName,
    );
  }

  factory PlayerEnteringGameDTO.fromMap(Map<String, dynamic> map) {
    return PlayerEnteringGameDTO(
      gameMode: EGameMode.fromString(map['gameMode'] as String),
      quizId: map['quizId'] as int,
      roomName: map['roomName'] as String,
      userData: UserEntity.fromJson(map['userData'] as Map<String, dynamic>),
      spotName: map['spotName'] as String,
    );
  }

  @override
  Map<String, dynamic> toMap() {
    return {
      'gameMode': gameMode.toString(),
      'quizId': quizId,
      'roomName': roomName,
      'userData': userData.toMap(),
      'spotName': spotName,
    };
  }

  @override
  String toJson() => json.encode(toMap());

  factory PlayerEnteringGameDTO.fromJson(String source) =>
      PlayerEnteringGameDTO.fromMap(
          json.decode(source) as Map<String, dynamic>);

  @override
  String toString() {
    return 'PlayerEnteringGameDTO(gameMode: $gameMode, quizId: $quizId, roomName: $roomName, userData: $userData)';
  }

  @override
  bool operator ==(covariant PlayerEnteringGameDTO other) {
    if (identical(this, other)) return true;

    return other.gameMode == gameMode &&
        other.quizId == quizId &&
        other.roomName == roomName &&
        other.userData == userData;
  }

  @override
  int get hashCode {
    return gameMode.hashCode ^
        quizId.hashCode ^
        roomName.hashCode ^
        userData.hashCode;
  }
}
