import 'package:equatable/equatable.dart';
import 'package:quycky/app/features/game/domain/entities/question_option_entity.dart';
import 'package:quycky/app/features/game/domain/entities/room_entity.dart';
import 'package:quycky/app/features/game/domain/entities/socket_user_entity.dart';
import 'package:quycky/app/features/game/domain/entities/user_image_memory_data.dart';

// enum EGameState {
//   waitingPlayers,
//   starting,
//   answering,
//   reviewing,
//   endGameReview
// }

enum EGameState {
  waitingPlayers('waitingPlayers'),
  starting('starting'),
  answering('answering'),
  reviewing('reviewing'),
  endGameReview('endGameReview'),
  closed('closed');

  final String label;

  @override
  String toString() {
    return label;
  }

  const EGameState(this.label);

  static EGameState fromString(String label) {
    return values.firstWhere(
      (v) => v.label == label,
      orElse: () => EGameState.closed,
    );
  }
}

// fourRandom: (<PERSON><PERSON> padr<PERSON>) O jogador entra em uma partida com outros players aleatórios
//podendo ser amigos ou não;
// oneOnInvitedPlayers: O jogador e outro que ele convidou
// enum EGameMode { fourRandom, oneOnInvitedPlayers }
enum EGameMode {
  singlePlayer('singlePlayer'),
  fourRandom('fourRandom'),
  oneOnInvitedPlayers('oneOnInvitedPlayers'),
  spotMatch('spotMatch');

  final String label;

  @override
  String toString() {
    return label;
  }

  const EGameMode(this.label);

  static EGameMode fromString(String label) {
    return values.firstWhere(
      (v) => v.label == label,
      orElse: () => EGameMode.singlePlayer,
    );
  }
}

enum EGameStartStage { none, preparing, starting }

enum EGamePontuationType { cold, fire }

class GameEntity extends Equatable {
  final bool alreadyGavePoint;
  final EGameStartStage gameStartStage;
  final int numberOfGamesPlayedByLoggedUser;
  final List<EGamePontuationType> receivedPontuations;
  final EGameState currentState;
  final RoomEntity room;
  final String userWhoReceivedPoint;
  final EGamePontuationType receivedPontuationType;
  final bool alreadyPlayed;
  final EGameMode gameMode;
  final bool showAllInviteSlots;
  final Map<int, SocketUserEntity> invitedPlayers;
  final bool isRoomOwner;
  final QuestionOptionEntity? selectedQuestionOption;
  final List<UserImageMemoryData> friendsImages;
  final bool showPointButtons;

  const GameEntity({
    this.numberOfGamesPlayedByLoggedUser = 0,
    this.showAllInviteSlots = false,
    this.alreadyGavePoint = false,
    this.showPointButtons = true,
    this.friendsImages = const [],
    this.gameStartStage = EGameStartStage.none,
    required this.invitedPlayers,
    this.currentState = EGameState.waitingPlayers,
    required this.room,
    required this.receivedPontuations,
    this.selectedQuestionOption,
    this.userWhoReceivedPoint = '',
    this.receivedPontuationType = EGamePontuationType.cold,
    this.gameMode = EGameMode.fourRandom,
    this.alreadyPlayed = false,
    this.isRoomOwner = true,
  });

  copyWith(
          {EGameMode? gameMode,
          int? numberOfGamesPlayedByLoggedUser,
          bool? alreadyGavePoint,
          EGameStartStage? gameStartStage,
          List<UserImageMemoryData>? friendsImages,
          EGameState? currentState,
          bool? showAllInviteSlots,
          Map<int, SocketUserEntity>? invitedPlayers,
          String? userWhoReceivedPoint,
          QuestionOptionEntity? selectedQuestionOption,
          bool updateSelectedQuestionOption = false,
          EGamePontuationType? receivedPontuationType,
          List<EGamePontuationType>? receivedPontuations,
          bool? alreadyPlayed,
          bool? isRoomOwner,
          bool? showPointButtons,
          RoomEntity? room}) =>
      GameEntity(
        showPointButtons: showPointButtons ?? this.showPointButtons,
        friendsImages: friendsImages ?? this.friendsImages,
        selectedQuestionOption:
            updateSelectedQuestionOption || selectedQuestionOption != null
                ? selectedQuestionOption
                : this.selectedQuestionOption,
        gameStartStage: gameStartStage ?? this.gameStartStage,
        numberOfGamesPlayedByLoggedUser: numberOfGamesPlayedByLoggedUser ??
            this.numberOfGamesPlayedByLoggedUser,
        showAllInviteSlots: showAllInviteSlots ?? this.showAllInviteSlots,
        gameMode: gameMode ?? this.gameMode,
        invitedPlayers: invitedPlayers ?? this.invitedPlayers,
        alreadyGavePoint: alreadyGavePoint ?? this.alreadyGavePoint,
        receivedPontuations: receivedPontuations ?? this.receivedPontuations,
        currentState: currentState ?? this.currentState,
        room: room ?? this.room,
        isRoomOwner: isRoomOwner ?? this.isRoomOwner,
        userWhoReceivedPoint: userWhoReceivedPoint ?? this.userWhoReceivedPoint,
        receivedPontuationType:
            receivedPontuationType ?? this.receivedPontuationType,
        alreadyPlayed: alreadyPlayed ?? this.alreadyPlayed,
      );

  @override
  List<Object> get props => [room];
}
