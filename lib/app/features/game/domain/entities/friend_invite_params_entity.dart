import 'dart:convert';

class FriendInviteParamsEntity {
  final String friendIdentifier;
  final String roomName;
  final String id;
  FriendInviteParamsEntity({
    required this.id,
    required this.friendIdentifier,
    required this.roomName,
  });

  FriendInviteParamsEntity copyWith({
    String? id,
    String? friendIdentifier,
    String? roomName,
  }) =>
      FriendInviteParamsEntity(
        friendIdentifier: friendIdentifier ?? this.friendIdentifier,
        roomName: roomName ?? this.roomName,
        id: id ?? this.id,
      );

  factory FriendInviteParamsEntity.fromRawJson(String str) =>
      FriendInviteParamsEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toJson());

  factory FriendInviteParamsEntity.fromJson(Map<String, dynamic> json) =>
      FriendInviteParamsEntity(
        friendIdentifier: json["friend_identifier"],
        roomName: json["room_name"],
        id: json["id"],
      );

  Map<String, dynamic> toJson() => {
        "friend_identifier": friendIdentifier,
        "id": id,
        "room_name": roomName,
      };
}
