import 'package:flutter_triple/flutter_triple.dart';
import 'package:quycky/app/features/friendship/domain/entities/chat_store_entity.dart';
import 'package:quycky/app/features/friendship/domain/entities/chat_message_entity.dart';

class ChatStore extends Store<ChatStoreEntity> {
  ChatStore()
      : super(const ChatStoreEntity(
          unreadMessagesCount: {},
          currentMessages: [],
          hasNewMessages: false,
          localUserIdentifier: '',
        ));

  void setCurrentConversationFriendIdentifier(String friendIdentifier) {
    update(
        state.copyWith(currentConversationFriendIdentifier: friendIdentifier),
        force: true);
  }

  void setLocalUserIdentifier(String localUserIdentifier) {
    update(state.copyWith(localUserIdentifier: localUserIdentifier),
        force: true);
  }

  /// Atualiza ou adiciona a contagem de mensagens não lidas para um usuário específico
  void updateUnreadCount(String userIdentifier, int count) {
    final updatedUnreadCount = Map<String, int>.from(state.unreadMessagesCount);
    updatedUnreadCount[userIdentifier] = count;

    update(
        state.copyWith(
          unreadMessagesCount: updatedUnreadCount,
        ),
        force: true);
  }

  /// Incrementa a contagem de mensagens não lidas para um usuário específico
  void incrementUnreadCount(String userIdentifier) {
    final currentCount = state.unreadMessagesCount[userIdentifier] ?? 0;
    updateUnreadCount(userIdentifier, currentCount + 1);
  }

  /// Zera a contagem de mensagens não lidas para um usuário específico
  void clearUnreadCount(String userIdentifier) {
    updateUnreadCount(userIdentifier, 0);
  }

  /// Atualiza a lista de mensagens atuais (quando getMessagesBetweenUsers é executado)
  void updateCurrentMessages(List<ChatMessageEntity> messages) {
    update(
        state.copyWith(
          currentMessages: messages,
        ),
        force: true);
  }

  /// Adiciona uma nova mensagem à lista atual
  void addMessage(ChatMessageEntity message) {
    final updatedMessages = List<ChatMessageEntity>.from(state.currentMessages);
    updatedMessages.add(message);
    updatedMessages.sort((a, b) => a.createdAt.compareTo(b.createdAt));
    update(
        state.copyWith(
          currentMessages: updatedMessages,
        ),
        force: true);
  }

  /// Obtém a contagem total de mensagens não lidas
  int get totalUnreadCount {
    return state.unreadMessagesCount.values
        .fold(0, (sum, count) => sum + count);
  }

  /// Obtém a contagem de mensagens não lidas para um usuário específico
  int getUnreadCountForUser(String userIdentifier) {
    if (userIdentifier == '') return 0;
    return state.unreadMessagesCount[userIdentifier] ?? 0;
  }

  bool get hasNewMessages => state.unreadMessagesCount.values.any(
        (count) => count > 0,
      );

  /// Limpa todos os dados do store
  void clear() {
    update(
        const ChatStoreEntity(
          unreadMessagesCount: {},
          currentMessages: [],
          hasNewMessages: false,
        ),
        force: true);
  }
}
