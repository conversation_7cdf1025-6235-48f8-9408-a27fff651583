import 'dart:convert';

import 'package:equatable/equatable.dart';
import 'package:quycky/core/utils/type_converters.dart';

class UserEntity extends Equatable {
  final String id;
  final String uuid;
  final String? appleId;
  final String? googleId;
  final String name;
  final String? email;
  final String? phoneNumber;
  final String? identifier;
  final String? onesignalId;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? avatar;
  final String? avatarUrl;
  final bool isConnected;
  final int pontuation;
  final DateTime? lastaccessAt;

  const UserEntity(
      {this.id = '',
      this.uuid = '',
      this.appleId = '',
      this.googleId = '',
      required this.name,
      this.email,
      this.phoneNumber,
      this.identifier,
      this.onesignalId = '',
      this.createdAt,
      this.updatedAt,
      this.avatar,
      this.avatarUrl,
      this.isConnected = false,
      this.lastaccessAt,
      this.pontuation = 0});

  @override
  List<Object> get props => [
        uuid,
        name,
      ];

  UserEntity copyWith({
    String? id,
    String? uuid,
    String? appleId,
    String? googleId,
    String? name,
    String? email,
    String? phoneNumber,
    String? identifier,
    String? onesignalId,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? avatar,
    String? avatarUrl,
    bool? isConnected,
    int? pontuation,
    DateTime? lastaccessAt,
  }) =>
      UserEntity(
          id: id ?? this.id,
          uuid: uuid ?? this.uuid,
          appleId: appleId ?? this.appleId,
          googleId: googleId ?? this.googleId,
          name: name ?? this.name,
          email: email ?? this.email,
          phoneNumber: phoneNumber ?? this.phoneNumber,
          identifier: identifier ?? this.identifier,
          onesignalId: onesignalId ?? this.onesignalId,
          avatar: avatar ?? this.avatar,
          isConnected: isConnected ?? this.isConnected,
          pontuation: pontuation ?? this.pontuation,
          lastaccessAt: lastaccessAt ?? this.lastaccessAt,
          avatarUrl: avatarUrl ?? this.avatarUrl);

  factory UserEntity.fromJson(Map<String, dynamic> json) => UserEntity(
        id: dynamicToString(json["id"]),
        uuid: dynamicToString(json["uuid"]),
        appleId: dynamicToString(json["apple_id"]),
        googleId: dynamicToString(json["google_id"]),
        name: dynamicToString(json["name"]),
        email: dynamicToString(json["email"]),
        phoneNumber: dynamicToString(json["phone_number"]),
        identifier: dynamicToString(json["identifier"]),
        onesignalId: dynamicToString(json["onesignal_id"]),
        avatar: dynamicToString(json["avatar"]),
        avatarUrl: dynamicToString(json["avatarUrl"]),
        lastaccessAt: json["lastaccess_at"] == null
            ? null
            : dynamicToDateTime(json["lastaccess_at"]),
        isConnected: json["isConnected"] != null
            ? json['isConnected'] is bool
                ? json['isConnected']
                : stringToBoolean(json["isConnected"].toString())
            : false,
        pontuation: json["pontuation"] != null
            ? json["pontuation"] is int
                ? json["pontuation"]
                : int.parse(json["pontuation"])
            : 0,
      );

  factory UserEntity.fromRawJson(String str) =>
      UserEntity.fromJson(json.decode(str));

  String toRawJson() => json.encode(toMap());

  Map<String, dynamic> toMap() => {
        "id": id,
        "uuid": uuid,
        "apple_id": appleId,
        "google_id": googleId,
        "name": name,
        "email": email,
        "phone_number": phoneNumber,
        "identifier": identifier,
        "onesignal_id": onesignalId,
        "avatar": avatar,
        "avatarUrl": avatarUrl,
        "isConnected": isConnected,
        "pontuation": pontuation,
        "lastaccess_at": lastaccessAt?.toIso8601String(),
      };
}
