import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_triple/flutter_triple.dart';
import 'package:lottie/lottie.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/friendship/domain/entities/friendship_store_entity.dart';
import 'package:quycky/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:quycky/app/features/friendship/presenter/services/chat_socket_handler.dart';
import 'package:quycky/app/features/friendship/presenter/store/friendship_store.dart';
import 'package:quycky/app/features/friendship/presenter/store/chat_store.dart';
import 'package:quycky/app/features/game/domain/entities/game_entity.dart';
import 'package:quycky/app/features/game/presenter/controllers/game_controller.dart';
import 'package:quycky/app/features/game/presenter/storage/game_storage.dart';
import 'package:quycky/app/features/home/<USER>/controllers/home_controller.dart';
import 'package:quycky/app/features/home/<USER>/controllers/question_temperature_slider_store.dart';
import 'package:quycky/app/features/home/<USER>/pages/widgets/animated_top_text.dart';
import 'package:quycky/app/features/home/<USER>/pages/widgets/promo_slides.dart';
import 'package:quycky/app/features/home/<USER>/pages/widgets/promotion_gift_notice.dart';
import 'package:quycky/app/features/home/<USER>/store/promotion_store.dart';
import 'package:quycky/app/features/user/domain/entities/user_login_return_entity.dart';
import 'package:quycky/app/features/user/presenter/controllers/user_controller.dart';
import 'package:quycky/app/features/user/presenter/store/user_store.dart';
import 'package:quycky/app/services/remote_config/remote_config_entity.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/widgets/custom_image.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/core/entities/promotion_entity.dart';
import 'package:quycky/core/entities/promotion_store_entity.dart';
import 'package:quycky/core/services/push_service/push_service.dart';
import 'package:quycky/core/utils/app_routes.dart';
import 'package:quycky/core/utils/assets_strings.dart';
import 'package:quycky/core/utils/interval_utils.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/core/utils/show_promotional_banner.dart';
import 'package:quycky/core/utils/use_cases/open_to_invited_game_room_usecase.dart';
import 'package:sizer/sizer.dart';

class HomePage extends StatefulWidget {
  final String title;
  const HomePage({super.key, this.title = "Home"});

  @override
  _HomePageState createState() => _HomePageState();
}

class _HomePageState extends State<HomePage>
    with SingleTickerProviderStateMixin {
  final _questionTemperatureSliderStore =
      Modular.get<QuestionTemperatureSliderStore>();
  final _chatSocketHandler = Modular.get<ChatSocketHandler>();
  final _gameStorage = Modular.get<GameStorage>();
  final _userStore = Modular.get<UserStore>();
  final _controller = Modular.get<HomeController>();
  final _friendshipController = Modular.get<FriendshipController>();
  final _friendshipStore = Modular.get<FriendshipStore>();
  final _chatStore = Modular.get<ChatStore>();
  final _promotionStore = Modular.get<PromotionStore>();
  final _userController = Modular.get<UserController>();

  late final AnimationController _circlesAnimationController;

  bool _showPromotions = false;

  double getWidth() {
    double defaultValue = 200;
    double res = MediaQuery.of(context).size.width * 0.85;
    return res > defaultValue ? defaultValue : res;
  }

  @override
  initState() {
    // _gameStorage.getGameRoomWaiting().then((value) {
    //   if (value != '') {
    //     Modular.to.pushReplacementNamed(AppRoutes.gameLobby());
    //   }
    //   getUserData();
    // }).catchError((err) => print(err));
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual,
        overlays: SystemUiOverlay.values);
    final pushNotification = Modular.get<PushService>();
    pushNotification.showRegisteredNotification();
    // if (!kIsWeb && Platform.isIOS) {
    //   getAutoStartPermission();
    // }
    _friendshipController.getAllFriendshipData();
    _questionTemperatureSliderStore.setValue(50.0);

    _circlesAnimationController = AnimationController(vsync: this);
    // final resPromo = _promotionStore.getPromotionById('1');
    // if (resPromo != null) {
    //   _promotionStore.changePromotionData(resPromo.copyWith(status: 'waiting'));
    // }
    // setTimeout(
    //     callback: _addPromotionToTest, duration: const Duration(seconds: 3));
    // _test();
    _tryToStartANewGame();
    _chatSocketHandler.fetchUnreadCount();
  }

  void _tryToStartANewGame() async {
    final openToInvitedGameRoomUseCaseUseCase =
        Modular.get<OpenToInvitedGameRoomUseCase>();
    final roomName = await _gameStorage.getGameRoomWaiting();

    if (roomName.isEmpty) return;

    setTimeout(
        callback: () => openToInvitedGameRoomUseCaseUseCase(
            OpenToInvitedGameRoomData(
                roomName: roomName, fromForegroundNotification: false)),
        duration: Duration(seconds: 1));
  }

  void _test() {
    final bannerToShow = BannerPromoRuleAndData(
        whenUserHasPlayed: 100,
        imageUrl:
            'https://quycky-space.s3.eu-central-1.amazonaws.com/ads/PopUp_box_326x359.png',
        url: 'https://app.quycky.co/redeem/prize/100?t=1',
        minutesToRedeem: 30);
    final promotionStore = Modular.get<PromotionStore>();
    promotionStore.addPromotion(PromotionEntity(
        identifier: bannerToShow.whenUserHasPlayed.toString(),
        name: 'Promotion',
        url: bannerToShow.url,
        status: 'waiting',
        startTime: DateTime.now().subtract(Duration(minutes: 38)),
        minutesToClose: bannerToShow.minutesToRedeem));
    setTimeout(
        callback: () => ShowPromotionalBanner(
            banner: bannerToShow,
            url: bannerToShow.url,
            imageUrl: bannerToShow.imageUrl),
        duration: const Duration(seconds: 1));
  }

  _HomePageState() {
    stopSocket();
  }

  getUserData() async {
    await _userStore.getLoginResultData();
    final user = await _userController.getUserById(_userStore.state.user.id);
    if (user != null) {
      setTimeout(
          callback: () => _userStore.setUser(user),
          duration: const Duration(seconds: 2));
    }
  }

  _addPromotionToTest() {
    // final remoteConfigStore = Modular.get<RemoteConfigStore>();
    // _promotionStore.addPromotion(PromotionEntity(
    //     identifier: '_first_end_game',
    //     name: 'First End Game Promotion',
    //     url: remoteConfigStore.state.promoUrlEndgame,
    //     startTime: DateTime.now()));
    _promotionStore.addPromotion(PromotionEntity(
        identifier: '3',
        startTime: DateTime.now().subtract(Duration(minutes: 29, seconds: 40)),
        minutesToClose: 30,
        name: 'Teste Temp',
        url: 'https://app.quycky.co/redeem/prize/3',
        status: 'waiting'));
  }

  stopSocket() {
    try {
      final gameController = Modular.get<GameController>();
      gameController.stopSocket();
    } catch (e) {}
  }

  handleOpenTutorial() =>
      Modular.to.pushReplacementNamed(AppRoutes.tutorial, arguments: true);

  Widget getTextWidgetSubtitleCaption(String subtitle, String caption) =>
      Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 20.0, bottom: 15),
            child: Text(
              subtitle,
              style: const TextStyle(
                  letterSpacing: 1.2,
                  fontFamily: "Roboto",
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                  fontSize: 12),
            ),
          ),
          Text(
            caption,
            style: const TextStyle(
                letterSpacing: 1.2,
                fontFamily: "Roboto",
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 10),
          ),
        ],
      );
  Widget getRightHeaderWidget() {
    return TripleBuilder(
        store: _userStore,
        builder: ((context, triple) {
          UserLoginResultEntity state =
              (triple as Triple<UserLoginResultEntity>).state;
          return InkWell(
            onTap: () => handleGoTo(AppRoutes.userProfile()),
            child: SizedBox(
              height: 80,
              width: 70,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  SizedBox(
                    height: 50,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        Avatar(
                          size: 30,
                          onPressed: () => handleGoTo(AppRoutes.userProfile()),
                          addPhotoButton: false,
                          imagePath: state.user.avatarUrl,
                        ),
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: Container(
                            width: 37,
                            height: 17,
                            decoration: BoxDecoration(
                                border:
                                    Border.all(width: 2, color: Colors.white),
                                color: CustomColors.cultured,
                                borderRadius: const BorderRadius.only(
                                  topLeft: Radius.circular(20),
                                  topRight: Radius.circular(20),
                                  bottomLeft: Radius.circular(20),
                                  bottomRight: Radius.circular(20),
                                )),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceAround,
                              children: [
                                Image.asset(
                                  Assets.png.fire,
                                  width: 11,
                                ),
                                Text(
                                  state.user.pontuation.toString(),
                                  style: const TextStyle(
                                      fontFamily: "Roboto",
                                      fontWeight: FontWeight.w700,
                                      color: CustomColors.americanPurple,
                                      fontSize: 9),
                                ),
                              ],
                            ),
                          ),
                        )
                      ],
                    ),
                  ),
                ],
              ),
            ),
          );
        }));
  }

  Widget getTextWidgetSubtitles(double v) {
    if (v < 40) {
      return getTextWidgetSubtitleCaption("CURIOUS", "BE A LOVE EXPLORER!");
    }
    if (v < 60) {
      return getTextWidgetSubtitleCaption("HOT", "POP IT LIKE IT´S HOT!");
    }
    return getTextWidgetSubtitleCaption("SPICY", "SPICE, SPICE BABY");

    // return getTextWidgetSubtitleCaption('', '');
  }

  Widget getLeftHeaderButton() {
    return TripleBuilder(
        store: _friendshipStore,
        builder: (context, store) {
          // Check for pending friendship invites OR new chat messages
          bool hasPendingInvites =
              _friendshipController.hasReceivedInvitesPendent();

          return Stack(
            children: [
              IconButton(
                  onPressed: () => handleGoTo(AppRoutes.userFriendsList),
                  icon: const Icon(
                    QuyckyIcons.heart_2,
                    color: Colors.white,
                    size: 21,
                  )),
              TripleBuilder(
                  store: _chatStore,
                  builder: (context, snapshot) {
                    bool hasNewChatMessages = _chatStore.hasNewMessages;

                    Color inviteNotificationColor =
                        (hasPendingInvites || hasNewChatMessages)
                            ? Colors.red
                            : Colors.transparent;

                    return Positioned(
                      right: 6,
                      top: 8,
                      child: Container(
                        width: 12,
                        height: 12,
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(30),
                            color: inviteNotificationColor),
                      ),
                    );
                  })
            ],
          );
        });
  }

  Widget getAppHeader() {
    // String title = '';
    // String subtitle = '';

    return AppHeader(
        logoSectionLeftWidget: getLeftHeaderButton(),
        // subtitle: subtitle,
        // title: title,
        // topPadding: Platform.isIOS ? 0 : null,
        noTitleSubtitleSpace: true,
        logoSectionRightWidget: getRightHeaderWidget());
  }

  void handleGoTo(String route, {bool replaceRoute = false}) {
    if (replaceRoute) {
      Modular.to.pushReplacementNamed(route);
      return;
    }
    Modular.to.pushNamed(route);
  }

  void handleGoToLobby() async {
    await Analytics.instance.logEvent(name: 'play_lobby');
    handleGoTo(AppRoutes.gameLobby(), replaceRoute: true);
  }

  void closePromotion(PromotionEntity promotion) {
    _promotionStore.changePromotionData(promotion.copyWith(
      status: 'expired',
    ));
    // _promotionStore.removePromotion(widget.promotion.identifier);
  }

  void _verifyAndCloseExpiredPromotions() {
    List<PromotionEntity> promotions = _promotionStore.state.promotions;
    for (var promotion in promotions) {
      if (promotion.status == 'waiting' && promotion.minutesToClose > 0) {
        final timeDifference = DateTime.now().difference(promotion.startTime);
        if (timeDifference.inMinutes >= promotion.minutesToClose) {
          closePromotion(promotion);
        }
      }
    }
  }

  Widget getPromotionNotice() {
    _verifyAndCloseExpiredPromotions();

    List<PromotionEntity> promotions = _promotionStore.state.promotions;

    if (promotions.isEmpty) {
      return const SizedBox(
        height: 0,
      );
    }

    PromotionEntity? promoToOpen;
    final filteredPromotions =
        promotions.where((promotion) => promotion.status == 'waiting').toList();
    if (filteredPromotions.isEmpty) {
      return const SizedBox(
        height: 0,
      );
    }

    final withTimerPromotions = filteredPromotions
        .where((promotion) => promotion.minutesToClose > 0)
        .toList();
    if (withTimerPromotions.isNotEmpty) {
      promoToOpen = withTimerPromotions[0];
    } else {
      promoToOpen = filteredPromotions[0];
    }

    return Flex(
      direction: Axis.vertical,
      children: [
        Container(
            width: 50,
            decoration: const BoxDecoration(
              gradient: LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    CustomColors.orangeSoda10,
                    CustomColors.orangeSoda,
                  ],
                  stops: [
                    0.1,
                    1,
                  ]),
              borderRadius: BorderRadius.vertical(
                  bottom: Radius.circular(60), top: Radius.circular(60)),
            ),
            margin: const EdgeInsets.only(right: 6),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.only(bottom: 14.0),
                  child: PromotionGiftNotice(
                    promotion: promoToOpen,
                    textTopPadding: 0,
                    fontSize: 10,
                    textColor: Colors.white,
                    imageHeight: 65,
                    imageWidth: 65,
                    onPressed: () {
                      // ShowPromotionDialog(promoToOpen);
                      _controller.logPromotionShowEvent(promotions[0]);
                      _handleShowPromotionSlides();
                      // _controller.logPromotionOpenedEvent();
                    },
                  ),
                ),
              ],
            )),
      ],
    );
  }

  void showNotification() async {}

  void _handleShowPromotionSlides() {
    _showPromotions = true;
    setState(() {});
  }

  void _handlePromotionSlidesClose() {
    _showPromotions = false;
    setState(() {});
  }

  Widget _buildPromotionOverlay() {
    if (!_showPromotions) return Container();

    return PromotionSlides(
      onClose: _handlePromotionSlidesClose,
    );
  }

  Widget getCenterRadar() {
    final widgets = [
      SizedBox(
        height: 400,
        width: double.infinity,
        child: Center(
          child: Stack(
            alignment: Alignment.center,
            children: [
              CustomImage(
                Assets.png.circlesRadar,
              ),
              TripleBuilder(
                  store: _userStore,
                  builder: (context, triple) {
                    return Avatar(
                      addPhotoButton: false,
                      imagePath: _userStore.state.user.avatarUrl,
                      size: 11.14.h,
                    );
                  }),
            ],
          ),
        ),
      ),
    ];
    return Column(
      children: widgets,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: GradientContainer(
        useDefault: true,
        coldOpacity: 0,
        normalOpacity: 0,
        hotOpacity: 0,
        child: SafeArea(
          child: Stack(
            children: [
              Stack(
                children: [
                  Padding(
                    padding: EdgeInsets.only(
                        right: 4,
                        top: (!kIsWeb && Platform.isIOS ? 60.0 : 80.0)),
                    child: Align(
                      alignment: Alignment.topRight,
                      child: TripleBuilder(
                          store: _promotionStore,
                          builder:
                              ((context, Triple<PromotionStoreEntity> store) =>
                                  getPromotionNotice() //store.state.promotions)
                              )),
                    ),
                  ),
                  Column(
                    children: [
                      getAppHeader(),
                      TripleBuilder(
                        store: _userStore,
                        builder: (context, triple) => AnimatedTopText(
                          playerName: _userStore.state.user.name,
                        ),
                      ),
                      const Spacer(),
                      getCenterRadar(),
                      const Spacer(),
                      SizedBox(
                        width: double.infinity,
                        child: Button(
                          onPressed: handleGoToLobby,
                          text: "START MATCH",
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.only(
                            top: 4.14.h, bottom: 0.8.h), //30.0),
                        child: IconButton(
                            onPressed: handleOpenTutorial,
                            icon: const Icon(
                              QuyckyIcons.question_circle,
                              color: Colors.white,
                            )),
                      ),
                    ],
                  ),
                ],
              ),
              _buildPromotionOverlay()
            ],
          ),
        ),
      ),
    );
  }
}
