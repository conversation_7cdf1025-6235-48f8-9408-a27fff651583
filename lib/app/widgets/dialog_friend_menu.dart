import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:quycky/analytics.dart';
import 'package:quycky/app/features/friendship/domain/entities/report_player_params_entity.dart';
import 'package:quycky/app/features/friendship/presenter/controllers/friendship_controller.dart';
import 'package:quycky/app/features/user/domain/entities/user_entity.dart';
import 'package:quycky/app/presenter/app_widget.dart';
import 'package:quycky/app/theme/colors.dart';
import 'package:quycky/app/widgets/app_header.dart';
import 'package:quycky/app/widgets/avatar.dart';
import 'package:quycky/app/widgets/block_player_dialog.dart';
import 'package:quycky/app/widgets/button.dart';
import 'package:quycky/app/widgets/gradient_container.dart';
import 'package:quycky/app/widgets/quycky_modal_progress.dart';
import 'package:quycky/app/widgets/report_player_dialog.dart';
import 'package:quycky/core/utils/quycky_icons_icons.dart';
import 'package:quycky/core/utils/show_message.dart';

enum ECurrentMenuState { initial, flagAbusiveContent, blockUser, endMenu }

class DialogFriendMenu extends StatefulWidget {
  final UserEntity user;
  final Uint8List? playerImageMemoryData;

  const DialogFriendMenu(
      {super.key, required this.user, required this.playerImageMemoryData});

  @override
  State<DialogFriendMenu> createState() => _DialogFriendMenu();
}

class _DialogFriendMenu extends State<DialogFriendMenu> {
  bool isClicked = false;
  bool isLoading = false;
  String title = 'ADD FRIEND';
  String subtitle = '';
  ECurrentMenuState currentMenuState = ECurrentMenuState.initial;
  final _friendshipController = Modular.get<FriendshipController>();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Analytics.instance.logEvent(name: 'play_open_profile');
  }

  void handleClose() => Navigator.pop(context);
  void onPressed() {
    handleClose();
    //  widget.onPressed!();
  }

  Widget getButton(String label,
          {color = CustomColors.button,
          borderColor = CustomColors.button,
          textColor = Colors.white,
          bool outlined = false,
          void Function()? onPressed}) =>
      Container(
        margin: const EdgeInsets.only(bottom: 14),
        constraints: const BoxConstraints(maxWidth: 325),
        width: double.infinity,
        height: 51,
        child: Button(
          onPressed: onPressed,
          outlined: outlined,
          autoSized: true,
          textColor: textColor,
          color: color,
          text: label,
          borderColor: borderColor,
        ),
      );
  setLoading(bool loadingState) {
    setState(() {
      isLoading = loadingState;
    });
  }

  handleAddFriend() async {
    setLoading(true);
    _friendshipController.addFriend(widget.user.id);
    setLoading(false);
    setState(() {
      subtitle = 'FRIEND REQUEST SENT';
      currentMenuState = ECurrentMenuState.endMenu;
    });
  }

  handleBlockUser() {
    setState(() {
      title = 'USER BLOCKED';
      subtitle = '';
      currentMenuState = ECurrentMenuState.endMenu;
    });
  }

  Widget getInitialMenuButtons() {
    return Column(
      children: [
        getButton('Add Friend',
            onPressed: handleAddFriend,
            textColor: CustomColors.primary,
            color: Colors.white),
        getButton('FLAG ABUSIVE CONTENT',
            onPressed: _doReportPlayer,
            outlined: true,
            borderColor: Colors.white,
            color: Colors.white),
        getButton('Block User',
            onPressed: handleBlockUser,
            outlined: true,
            borderColor: Colors.white,
            color: Colors.white),
      ],
    );
  }

  handleGoToFlagAbusiveContentMenu() {
    setState(() {
      title = 'FLAG ABUSIVE CONTENT';
      subtitle = 'WHY DO YOU WANT TO REPORT?';
      currentMenuState = ECurrentMenuState.flagAbusiveContent;
    });
  }

  void _doBlockPlayer() async {
    BuildContext pageContext = AppWidget.globalKey.currentState!.context;
    final controller = Modular.get<FriendshipController>();
    final res = await controller.blockUser(int.parse(widget.user.id));
    if (res) {
      showDialog(
        context: pageContext, // Usa o context da página para o showDialog
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return BlockPlayerDialog(
            onDone: () {},
          );
        },
      );
      return;
    }
    ShowMessage(
      noAvatar: true,
      message:
          'There was a problem blocking the player, please try again later',
    );
  }

  void _doReportPlayer() async {
    BuildContext pageContext = AppWidget.globalKey.currentState!.context;
    final controller = Modular.get<FriendshipController>();
    final res = await controller.reportUser(
        int.parse(widget.user.id), ReportReason.abusiveContent);
    if (res == 1) {
      showDialog(
        context: pageContext,
        barrierDismissible: false,
        builder: (BuildContext dialogContext) {
          return ReportPlayerDialog(
            onBlockPlayer: () {
              _doBlockPlayer();
            },
            onReturn: () {},
          );
        },
      );
      return;
    }
    if (res == 0) {
      ShowMessage(
        noAvatar: true,
        message: 'This player has already been reported by you.',
        noButton: true,
        duration: Duration(seconds: 3),
      );
      return;
    }
    ShowMessage(
      noAvatar: true,
      message:
          'There was a problem reporting the player, please try again later',
      noButton: true,
      duration: Duration(seconds: 3),
    );
  }

  handleReportAbusiveContent() {
    setState(() {
      title = 'ABUSIVE CONTENT REPORTED';
      subtitle = '';
      currentMenuState = ECurrentMenuState.endMenu;
    });
  }

  Widget getEndMenuButtons() {
    return getButton('Back to Game',
        onPressed: handleClose,
        textColor: CustomColors.primary,
        color: Colors.white);
  }

  Widget getFlagAbusiveContentMenuButtons() {
    return Column(
      children: [
        getButton('Abusive Profile Pic',
            onPressed: handleReportAbusiveContent,
            outlined: true,
            borderColor: Colors.white,
            color: Colors.white),
        getButton('Abusive Answer',
            onPressed: handleReportAbusiveContent,
            outlined: true,
            borderColor: Colors.white,
            color: Colors.white),
        getButton('Abusive Username',
            onPressed: handleReportAbusiveContent,
            outlined: true,
            borderColor: Colors.white,
            color: Colors.white),
      ],
    );
  }

  Widget getButtons() {
    switch (currentMenuState) {
      case ECurrentMenuState.initial:
        return getInitialMenuButtons();
      case ECurrentMenuState.flagAbusiveContent:
        return getFlagAbusiveContentMenuButtons();
      case ECurrentMenuState.endMenu:
        return getEndMenuButtons();
    }
    return getInitialMenuButtons();
  }

  Widget getUserAvatarAndName() {
    return Column(
      children: [
        SizedBox(
          height: 130,
          width: 105,
          child: Stack(
            alignment: Alignment.center,
            children: [
              Avatar(
                size: 100,
                addPhotoButton: false,
                imageMemory: widget.playerImageMemoryData,
                // imagePath: widget.user.avatarUrl,
              ),
              Align(
                alignment: Alignment.bottomCenter,
                child: Container(
                  width: 82,
                  height: 33,
                  decoration: BoxDecoration(
                      border: Border.all(width: 2, color: Colors.white),
                      color: CustomColors.cultured,
                      borderRadius: const BorderRadius.only(
                        topLeft: Radius.circular(20),
                        topRight: Radius.circular(20),
                        bottomLeft: Radius.circular(20),
                        bottomRight: Radius.circular(20),
                      )),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Image.asset(
                        'assets/img/png/fire.png',
                        width: 20,
                      ),
                      const Text(
                        '0',
                        style: TextStyle(
                            letterSpacing: 3,
                            fontFamily: "Roboto",
                            fontWeight: FontWeight.bold,
                            color: CustomColors.americanPurple,
                            fontSize: 12),
                      ),
                    ],
                  ),
                ),
              )
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 24),
          child: Text(
            widget.user.name,
            style: const TextStyle(
                letterSpacing: 3,
                fontFamily: "Roboto",
                fontWeight: FontWeight.bold,
                color: Colors.white,
                fontSize: 10),
          ),
        )
      ],
    );
  }

  Widget getWidget() {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(top: 41.0),
          child: getUserAvatarAndName(),
        ),
        Padding(
          padding: const EdgeInsets.only(top: 40.0),
          child: getButtons(),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        body: GradientContainer(
            useDefault: true,
            coldOpacity: 0,
            normalOpacity: 0,
            hotOpacity: 0,
            child: SafeArea(
                child: QuyckyModalProgress(
              state: isLoading,
              child: Column(
                children: [
                  AppHeader(
                      title: title,
                      subtitle: subtitle,
                      logoSectionLeftWidget: IconButton(
                        icon: const Icon(
                          QuyckyIcons.arrow_left_circle,
                          color: Colors.white,
                          size: 23,
                        ),
                        onPressed: handleClose,
                      )),
                  getWidget(),
                  const Spacer(),
                  const Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      IconButton(
                          onPressed: null,
                          icon: Icon(
                            QuyckyIcons.question_circle,
                            color: Colors.white,
                          ))
                    ],
                  ),
                  const SizedBox(height: 30),
                ],
              ),
            ))));
  }
}
